accelerate==1.7.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.11
aiosignal==1.3.2
airportsdata==20250622
annotated-types==0.7.0
anyio==4.9.0
astor==0.8.1
asttokens==3.0.0
async-timeout==4.0.3
attrs==25.3.0
bitsandbytes==0.46.0
blake3==1.0.5
blobfile==3.0.0
cachetools==6.1.0
certifi==2025.4.26
charset-normalizer==3.4.2
click==8.1.8
cloudpickle==3.1.1
comm==0.2.2
compressed-tensors==0.10.1
cupy-cuda12x==13.4.1
cut-cross-entropy==25.1.1
dataclasses-json==0.6.7
datasets==3.6.0
debugpy==1.8.14
decorator==5.2.1
depyf==0.18.0
diffusers==0.34.0
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
docstring-parser==0.16
einops==0.8.1
email-validator==2.2.0
exceptiongroup==1.3.0
executing==2.2.0
fastapi==0.115.12
fastapi-cli==0.0.7
fastrlock==0.8.3
filelock==3.18.0
frozenlist==1.6.2
fsspec==2025.3.0
genson==1.3.0
gguf==0.17.1
gitdb==4.0.12
gitpython==3.1.44
googleapis-common-protos==1.70.0
greenlet==3.2.3
groq==0.28.0
grpcio==1.73.0
grpcio-tools==1.73.0
grpclib==0.4.7
h11==0.16.0
h2==4.2.0
hf-transfer==0.1.9
hf-xet==1.1.3
hpack==4.1.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.1
huggingface-hub==0.32.4
hyperframe==6.1.0
idna==3.10
importlib-metadata==8.7.0
inquirerpy==0.3.4
instructor==1.8.3
interegular==0.3.3
ipykernel==6.29.5
ipython==8.37.0
ipywidgets==8.1.7
iso3166==2.1.1
jedi==0.19.2
jinja2==3.1.6
jiter==0.8.2
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter-client==8.6.3
jupyter-core==5.8.1
jupyterlab-widgets==3.0.15
langchain==0.3.26
langchain-community==0.3.26
langchain-core==0.3.66
langchain-text-splitters==0.3.8
langsmith==0.4.2
lark==1.2.2
llguidance==0.7.30
llvmlite==0.44.0
lm-format-enforcer==0.10.11
lxml==5.4.0
markdown-it-py==3.0.0
markupsafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
mdurl==0.1.2
mistral-common==1.6.2
modal==1.0.3
mpmath==1.3.0
msgpack==1.1.1
msgspec==0.19.0
multidict==6.4.4
multiprocess==0.70.16
mypy-extensions==1.1.0
nest-asyncio==1.6.0
networkx==3.4.2
ninja==********
numba==0.61.2
numpy==2.2.6
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-cufile-cu12==********
nvidia-curand-cu12==*********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==********
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
ollama==0.5.1
openai==1.86.0
opencv-python-headless==*********
opentelemetry-api==1.34.1
opentelemetry-exporter-otlp==1.34.1
opentelemetry-exporter-otlp-proto-common==1.34.1
opentelemetry-exporter-otlp-proto-grpc==1.34.1
opentelemetry-exporter-otlp-proto-http==1.34.1
opentelemetry-proto==1.34.1
opentelemetry-sdk==1.34.1
opentelemetry-semantic-conventions==0.55b1
opentelemetry-semantic-conventions-ai==0.4.9
orjson==3.10.18
outlines==1.0.2
outlines-core==0.1.26
packaging==24.2
pandas==2.3.0
parso==0.8.4
partial-json-parser==*******.post6
peft==0.15.2
pexpect==4.9.0
pfzy==0.3.4
pillow==11.2.1
pip==25.1.1
platformdirs==4.3.8
prometheus-client==0.22.1
prometheus-fastapi-instrumentator==7.1.0
prompt-toolkit==3.0.51
propcache==0.3.1
protobuf==3.20.3
psutil==7.0.0
ptyprocess==0.7.0
pure-eval==0.2.3
py-cpuinfo==9.0.0
pyarrow==20.0.0
pycountry==24.6.1
pycryptodomex==3.23.0
pydantic==2.11.5
pydantic-core==2.33.2
pydantic-settings==2.10.1
pygments==2.19.2
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-json-logger==3.3.0
python-multipart==0.0.20
pytz==2025.2
pyyaml==6.0.2
pyzmq==26.4.0
ray==2.47.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==13.9.4
rich-toolkit==0.14.7
rpds-py==0.25.1
safetensors==0.5.3
scikit-learn==1.7.0
scipy==1.15.3
sentencepiece==0.2.0
sentry-sdk==2.31.0
setproctitle==1.3.6
setuptools==80.9.0
shellingham==1.5.4
shtab==1.7.2
sigtools==4.0.1
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
sqlalchemy==2.0.41
stack-data==0.6.3
starlette==0.46.2
sympy==1.14.0
synchronicity==0.9.13
tenacity==9.1.2
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
toml==0.10.2
torch==2.7.0
torchaudio==2.7.0
torchvision==0.22.0
tornado==6.5.1
tqdm==4.67.1
traitlets==5.14.3
transformers==4.52.4
triton==3.3.0
trl==0.18.1
typeguard==4.4.4
typer==0.16.0
types-certifi==2021.10.8.3
types-toml==0.10.8.20240310
typing-extensions==4.14.0
typing-inspect==0.9.0
typing-inspection==0.4.1
tyro==0.9.25
tzdata==2025.2
unsloth==2025.6.8
unsloth-zoo==2025.6.6
urllib3==2.4.0
uvicorn==0.34.3
uvloop==0.21.0
vllm==0.9.1
wandb==0.20.1
watchfiles==1.0.5
wcwidth==0.2.13
websockets==15.0.1
wheel==0.45.1
widgetsnbextension==4.0.14
xformers==0.0.30
xgrammar==0.1.19
xxhash==3.5.0
yarl==1.20.0
zipp==3.23.0
zstandard==0.23.0
