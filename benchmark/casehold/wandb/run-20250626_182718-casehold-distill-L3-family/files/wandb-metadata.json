{"os": "Linux-6.8.0-59-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.12", "startedAt": "2025-06-26T11:27:18.867316Z", "program": "/home/<USER>/KD-SLM/benchmark/casehold/distill_casehold.py", "codePath": "benchmark/casehold/distill_casehold.py", "git": {"remote": "https://github.com/iSE-UET-VNU/KD-SLM.git", "commit": "50e0077f4dc2f2a1f294ad4e82338e79ed41a56c"}, "email": "<EMAIL>", "root": "/home/<USER>/KD-SLM/benchmark/casehold", "host": "csews-Precision-7920-Tower", "executable": "/home/<USER>/KD-SLM/venv/bin/python3", "codePathLocal": "distill_casehold.py", "cpu_count": 6, "cpu_count_logical": 6, "gpu": "NVIDIA RTX A4000", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "33291034624"}, "cpu": {"count": 6, "countLogical": 6}, "gpu_nvidia": [{"name": "NVIDIA RTX A4000", "memoryTotal": "17171480576", "cudaCores": 6144, "architecture": "Ampere", "uuid": "GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a"}], "cudaVersion": "12.8"}