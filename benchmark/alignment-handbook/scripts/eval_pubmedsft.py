import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from datasets import load_dataset
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, confusion_matrix
import warnings
warnings.filterwarnings("ignore")

# Path to your fine-tuned model
MODEL_PATH = "/drive1/nammt/KD-SLM/benchmark/alignment-handbook/llama3.2-1b-pubmedqa-sft"

# Use BitsAndBytesConfig for 4-bit quantization (future-proof)
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_quant_type="nf4",
    bnb_4bit_use_double_quant=False,
    bnb_4bit_compute_dtype=torch.float16,
)

# Load model and tokenizer in 4-bit, on CPU or GPU as available
model = AutoModelForCausalLM.from_pretrained(
    MODEL_PATH,
    device_map="auto",
    torch_dtype="auto",
    quantization_config=bnb_config,
    trust_remote_code=True,
)
tokenizer = AutoTokenizer.from_pretrained(MODEL_PATH, trust_remote_code=True)
tokenizer.padding_side = "left"
if tokenizer.pad_token_id is None:
    if tokenizer.eos_token_id is not None:
        tokenizer.pad_token_id = tokenizer.eos_token_id
    else:
        tokenizer.pad_token_id = 0  # fallback

# Load PubMedQA test set (or a subset for memory)
ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "pubmedqa", split="test[:5]")  # Only 5 samples for speed

def format_example(example):
    context_info = example.get('context', {})
    if isinstance(context_info, dict):
        contexts = context_info.get('contexts', [])
        context_text = " ".join(contexts) if isinstance(contexts, list) else str(context_info)
    elif isinstance(context_info, list):
        context_text = " ".join(context_info)
    else:
        context_text = str(context_info)
    question = example.get('question', '')
    user_prompt = f"""Based on the following medical context, please answer the question.\n\nQuestion: {question}\n\nContext: {context_text}\n\nPlease provide a detailed answer based on the medical evidence presented in the context."""
    return user_prompt, example.get('long_answer', '')

def extract_decision(text):
    for option in ["yes", "no", "maybe"]:
        if option in text.lower():
            return option
    return "unknown"

preds, labels = [], []
model.eval()
with torch.no_grad():
    for ex in ds:
        prompt, label = format_example(ex)
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=64).to(model.device)  # Shorter max_length
        output = model.generate(**inputs, max_new_tokens=16, pad_token_id=tokenizer.pad_token_id)  # Fewer tokens
        pred_text = tokenizer.decode(output[0], skip_special_tokens=True)
        preds.append(extract_decision(pred_text))
        labels.append(extract_decision(label))

acc = accuracy_score(labels, preds)
f1 = f1_score(labels, preds, average='micro', labels=["yes", "no", "maybe"])

print(f"Accuracy: {acc:.4f}")
print(f"F1 (micro/overall): {f1:.4f}")
print(f"Sample predictions: {list(zip(preds, labels))[:5]}")