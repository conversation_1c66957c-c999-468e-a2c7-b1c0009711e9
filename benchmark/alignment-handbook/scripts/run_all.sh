#!/bin/bash
# This script runs the fine-tuning process for all three datasets sequentially,
# ensuring that each run saves to a unique local directory and pushes to a unique Hub repository.

# --- Configuration ---
CONFIG_FILE="/drive1/nammt/KD-SLM/benchmark/alignment-handbook/recipes/llama3.2-1b/sft/config.yaml"
SCRIPT_NAME="train_sft.py"
HUB_USERNAME="MothMalone" 

# --- Run 1: casehold ---
echo "--- Starting Fine-Tuning for casehold ---"
accelerate launch $SCRIPT_NAME \
    $CONFIG_FILE \
    --dataset_name casehold \
    --output_dir ./results-casehold \
    --hub_model_id "$HUB_USERNAME/Llama-3.2-1B-SFT-casehold"

# --- Run 2: finqa ---
echo "--- Starting Fine-Tuning for finqa ---"
accelerate launch $SCRIPT_NAME \
    $CONFIG_FILE \
    --dataset_name finqa \
    --output_dir ./results-finqa \
    --hub_model_id "$HUB_USERNAME/Llama-3.2-1B-SFT-finqa"

# --- Run 3: pubmedqa ---
echo "--- Starting Fine-Tuning for pubmedqa ---"
accelerate launch $SCRIPT_NAME \
    $CONFIG_FILE \
    --dataset_name pubmedqa \
    --output_dir ./results-pubmedqa \
    --hub_model_id "$HUB_USERNAME/Llama-3.2-1B-SFT-pubmedqa"

echo "--- All fine-tuning jobs complete! ---"