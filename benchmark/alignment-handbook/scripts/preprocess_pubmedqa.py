#!/usr/bin/env python
# coding=utf-8
"""
Data preprocessing script for PubMedQA dataset to convert it into the conversational format
required by the SFT trainer.
"""

import logging
from datasets import load_dataset, DatasetDict
from typing import Dict, Any

logger = logging.getLogger(__name__)


def format_pubmedqa_example(example: Dict[str, Any]) -> Dict[str, Any]:
    """
    Formats a single PubMedQA example into the conversational format required by SFT.
    
    Args:
        example: A dictionary containing PubMedQA fields
        
    Returns:
        A dictionary with the 'messages' field in conversational format
    """
    # Extract the context information
    context_info = example.get('context', {})
    
    # Handle different context formats
    if isinstance(context_info, dict):
        # If context is a dict with 'contexts' field
        contexts = context_info.get('contexts', [])
        if isinstance(contexts, list) and len(contexts) > 0:
            # Join multiple context pieces
            context_text = " ".join(contexts)
        else:
            context_text = str(context_info)
    elif isinstance(context_info, list):
        # If context is directly a list
        context_text = " ".join(context_info)
    else:
        # If context is a string
        context_text = str(context_info)
    
    # Create the user prompt combining question and context
    question = example.get('question', '')
    
    # Create a comprehensive prompt for medical QA
    user_prompt = f"""Based on the following medical context, please answer the question.

Question: {question}

Context: {context_text}

Please provide a detailed answer based on the medical evidence presented in the context."""
    
    # Get the answer from long_answer field
    assistant_answer = example.get('long_answer', '')
    
    # Create the conversational format
    messages = [
        {"role": "user", "content": user_prompt},
        {"role": "assistant", "content": assistant_answer}
    ]
    
    return {"messages": messages}


def preprocess_pubmedqa_dataset(dataset_name: str = "MothMalone/SLMS-KD-Benchmarks", 
                               config_name: str = "pubmedqa") -> DatasetDict:
    """
    Load and preprocess the PubMedQA dataset for SFT training.
    
    Args:
        dataset_name: The name of the dataset on HuggingFace Hub
        config_name: The configuration name for the dataset
        
    Returns:
        A DatasetDict with preprocessed data in conversational format
    """
    logger.info(f"Loading dataset {dataset_name} with config {config_name}")
    
    # Load the dataset
    try:
        raw_dataset = load_dataset(dataset_name, config_name)
        logger.info(f"Successfully loaded dataset with splits: {list(raw_dataset.keys())}")
    except Exception as e:
        logger.error(f"Failed to load dataset: {e}")
        raise
    
    # Log dataset info
    for split_name, split_data in raw_dataset.items():
        logger.info(f"Split '{split_name}': {len(split_data)} examples")
        logger.info(f"Columns: {split_data.column_names}")
        
        # Show a sample
        if len(split_data) > 0:
            sample = split_data[0]
            logger.info(f"Sample from {split_name}:")
            for key, value in sample.items():
                if isinstance(value, str) and len(value) > 100:
                    logger.info(f"  {key}: {value[:100]}...")
                else:
                    logger.info(f"  {key}: {value}")
    
    # Preprocess each split
    processed_dataset = DatasetDict()
    
    for split_name, split_data in raw_dataset.items():
        logger.info(f"Processing split: {split_name}")
        
        # Apply the formatting function
        processed_split = split_data.map(
            format_pubmedqa_example,
            remove_columns=split_data.column_names,  # Remove original columns
            desc=f"Formatting {split_name} split for SFT"
        )
        
        processed_dataset[split_name] = processed_split
        logger.info(f"Processed {split_name}: {len(processed_split)} examples")
        
        # Show a sample of the processed data
        if len(processed_split) > 0:
            sample = processed_split[0]
            logger.info(f"Processed sample from {split_name}:")
            logger.info(f"  Messages: {len(sample['messages'])} turns")
            for i, msg in enumerate(sample['messages']):
                content_preview = msg['content'][:200] + "..." if len(msg['content']) > 200 else msg['content']
                logger.info(f"    {i+1}. {msg['role']}: {content_preview}")
    
    return processed_dataset


def main():
    """Main function to test the preprocessing."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s"
    )
    
    # Test the preprocessing
    try:
        processed_data = preprocess_pubmedqa_dataset()
        logger.info("Preprocessing completed successfully!")
        
        # Print summary
        for split_name, split_data in processed_data.items():
            logger.info(f"Final {split_name} split: {len(split_data)} examples")
            
    except Exception as e:
        logger.error(f"Preprocessing failed: {e}")
        raise


if __name__ == "__main__":
    main()
