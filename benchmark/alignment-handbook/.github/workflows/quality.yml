name: Quality

on:
  push:
    branches:
      - main
      - v*-release
  pull_request:
    branches:
      - main

jobs:

  check_code_quality:
    name: Check code quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Setup Python environment
        uses: actions/setup-python@v2
        with:
          python-version: 3.10.10
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install ".[quality]"
      - name: Code quality
        run: |
          make quality

