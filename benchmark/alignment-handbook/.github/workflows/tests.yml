name: Tests

on:
  push:
    branches:
      - main
      - v*-release
  pull_request:
    branches:
      - main

jobs:

  unit-tests:
    name: Run unit tests
    env:
      HF_TOKEN: ${{ secrets.HF_TOKEN }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Setup Python environment
        uses: actions/setup-python@v2
        with:
          python-version: 3.10.10
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install ".[dev, torch]"
      - name: Run unit tests
        run: HF_TOKEN=$HF_TOKEN pytest -sv tests/