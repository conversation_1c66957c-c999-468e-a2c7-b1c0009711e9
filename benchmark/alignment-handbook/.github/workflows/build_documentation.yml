name: Build documentation

on:
  push:
    branches:
      - main

jobs:
  build:
    uses: huggingface/doc-builder/.github/workflows/build_main_documentation.yml@main
    with:
      commit_sha: ${{ github.sha }}
      package: alignment-handbook
      path_to_docs: alignment-handbook/chapters/
      additional_args: --not_python_module
      languages: en
    secrets:
      hf_token: ${{ secrets.HF_DOC_BUILD_PUSH }}
