{"best_metric": null, "best_model_checkpoint": null, "epoch": 2.98961937716263, "eval_steps": 100, "global_step": 216, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.01384083044982699, "grad_norm": 1.060884952545166, "learning_rate": 6.818181818181818e-07, "loss": 2.3845, "step": 1}, {"epoch": 0.06920415224913495, "grad_norm": 0.9603719115257263, "learning_rate": 3.409090909090909e-06, "loss": 2.3628, "step": 5}, {"epoch": 0.1384083044982699, "grad_norm": 1.009284257888794, "learning_rate": 6.818181818181818e-06, "loss": 2.339, "step": 10}, {"epoch": 0.20761245674740483, "grad_norm": 0.803464412689209, "learning_rate": 1.0227272727272727e-05, "loss": 2.3091, "step": 15}, {"epoch": 0.2768166089965398, "grad_norm": 0.7160068154335022, "learning_rate": 1.3636363636363637e-05, "loss": 2.2835, "step": 20}, {"epoch": 0.3460207612456747, "grad_norm": 0.7629315257072449, "learning_rate": 1.499115119459693e-05, "loss": 2.2421, "step": 25}, {"epoch": 0.41522491349480967, "grad_norm": 0.8057928681373596, "learning_rate": 1.4937150759198188e-05, "loss": 2.2316, "step": 30}, {"epoch": 0.4844290657439446, "grad_norm": 0.7315549850463867, "learning_rate": 1.483441929296678e-05, "loss": 2.1657, "step": 35}, {"epoch": 0.5536332179930796, "grad_norm": 0.8234800100326538, "learning_rate": 1.4683629931409903e-05, "loss": 2.1163, "step": 40}, {"epoch": 0.6228373702422145, "grad_norm": 0.8279880285263062, "learning_rate": 1.4485770703581318e-05, "loss": 2.1118, "step": 45}, {"epoch": 0.6920415224913494, "grad_norm": 0.8164498209953308, "learning_rate": 1.4242138058140465e-05, "loss": 2.0074, "step": 50}, {"epoch": 0.7612456747404844, "grad_norm": 0.8191325068473816, "learning_rate": 1.395432836852944e-05, "loss": 1.9952, "step": 55}, {"epoch": 0.8304498269896193, "grad_norm": 0.8336239457130432, "learning_rate": 1.3624227472929082e-05, "loss": 2.0463, "step": 60}, {"epoch": 0.8996539792387543, "grad_norm": 0.8765895962715149, "learning_rate": 1.3253998317532396e-05, "loss": 1.9777, "step": 65}, {"epoch": 0.9688581314878892, "grad_norm": 0.7420470714569092, "learning_rate": 1.284606678410122e-05, "loss": 1.8931, "step": 70}, {"epoch": 1.0380622837370241, "grad_norm": 0.6955271363258362, "learning_rate": 1.2403105794669394e-05, "loss": 2.068, "step": 75}, {"epoch": 1.1072664359861593, "grad_norm": 0.7240490913391113, "learning_rate": 1.192801779754442e-05, "loss": 1.9043, "step": 80}, {"epoch": 1.1764705882352942, "grad_norm": 0.7493748664855957, "learning_rate": 1.1423915749365974e-05, "loss": 1.988, "step": 85}, {"epoch": 1.245674740484429, "grad_norm": 0.7611699104309082, "learning_rate": 1.0894102717834043e-05, "loss": 1.8967, "step": 90}, {"epoch": 1.314878892733564, "grad_norm": 0.7063078880310059, "learning_rate": 1.03420502387573e-05, "loss": 1.8339, "step": 95}, {"epoch": 1.3840830449826989, "grad_norm": 0.6557862162590027, "learning_rate": 9.77137556923463e-06, "loss": 1.807, "step": 100}, {"epoch": 1.3840830449826989, "eval_loss": 1.872956395149231, "eval_runtime": 15.6005, "eval_samples_per_second": 7.884, "eval_steps_per_second": 7.884, "step": 100}, {"epoch": 1.453287197231834, "grad_norm": 0.7677621245384216, "learning_rate": 9.185817986015471e-06, "loss": 1.8849, "step": 105}, {"epoch": 1.5224913494809689, "grad_norm": 0.6558657288551331, "learning_rate": 8.589214284341179e-06, "loss": 1.8206, "step": 110}, {"epoch": 1.5916955017301038, "grad_norm": 0.6721696853637695, "learning_rate": 7.985473637808268e-06, "loss": 1.8586, "step": 115}, {"epoch": 1.6608996539792389, "grad_norm": 0.6410223841667175, "learning_rate": 7.378551983981239e-06, "loss": 1.7736, "step": 120}, {"epoch": 1.7301038062283736, "grad_norm": 0.6548729538917542, "learning_rate": 6.77242610359014e-06, "loss": 1.8998, "step": 125}, {"epoch": 1.7993079584775087, "grad_norm": 0.6825141906738281, "learning_rate": 6.17106756315583e-06, "loss": 1.7779, "step": 130}, {"epoch": 1.8685121107266436, "grad_norm": 0.6834182143211365, "learning_rate": 5.578416691780707e-06, "loss": 1.8434, "step": 135}, {"epoch": 1.9377162629757785, "grad_norm": 0.6922764778137207, "learning_rate": 4.998356762618847e-06, "loss": 1.8636, "step": 140}, {"epoch": 2.0069204152249136, "grad_norm": 1.5079513788223267, "learning_rate": 4.434688548198331e-06, "loss": 2.1771, "step": 145}, {"epoch": 2.0761245674740483, "grad_norm": 0.6443067193031311, "learning_rate": 3.891105416318962e-06, "loss": 1.844, "step": 150}, {"epoch": 2.1453287197231834, "grad_norm": 0.6563525795936584, "learning_rate": 3.3711691297064735e-06, "loss": 1.854, "step": 155}, {"epoch": 2.2145328719723185, "grad_norm": 0.665769636631012, "learning_rate": 2.878286507993045e-06, "loss": 1.8654, "step": 160}, {"epoch": 2.283737024221453, "grad_norm": 0.6838922500610352, "learning_rate": 2.415687104943726e-06, "loss": 1.7943, "step": 165}, {"epoch": 2.3529411764705883, "grad_norm": 0.6858451962471008, "learning_rate": 1.986402047195999e-06, "loss": 1.846, "step": 170}, {"epoch": 2.422145328719723, "grad_norm": 0.644000232219696, "learning_rate": 1.5932441731690933e-06, "loss": 1.814, "step": 175}, {"epoch": 2.491349480968858, "grad_norm": 0.6679831743240356, "learning_rate": 1.2387896022804675e-06, "loss": 1.8079, "step": 180}, {"epoch": 2.5605536332179932, "grad_norm": 0.6555227041244507, "learning_rate": 9.25360855234893e-07, "loss": 1.7896, "step": 185}, {"epoch": 2.629757785467128, "grad_norm": 0.6432757377624512, "learning_rate": 6.550116359884498e-07, "loss": 1.7757, "step": 190}, {"epoch": 2.698961937716263, "grad_norm": 0.6701865196228027, "learning_rate": 4.29513375101743e-07, "loss": 1.874, "step": 195}, {"epoch": 2.7681660899653977, "grad_norm": 0.6579903960227966, "learning_rate": 2.503436226554165e-07, "loss": 1.8282, "step": 200}, {"epoch": 2.7681660899653977, "eval_loss": 1.8537700176239014, "eval_runtime": 14.1799, "eval_samples_per_second": 8.674, "eval_steps_per_second": 8.674, "step": 200}, {"epoch": 2.837370242214533, "grad_norm": 0.6383829116821289, "learning_rate": 1.1867636678194322e-07, "loss": 1.8356, "step": 205}, {"epoch": 2.906574394463668, "grad_norm": 0.6873584985733032, "learning_rate": 3.53743412503954e-08, "loss": 1.821, "step": 210}, {"epoch": 2.9757785467128026, "grad_norm": 0.6732977032661438, "learning_rate": 9.833725077584844e-10, "loss": 1.8064, "step": 215}], "logging_steps": 5, "max_steps": 216, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 5225701875646464.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}