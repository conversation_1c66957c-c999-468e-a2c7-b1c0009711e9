#!/usr/bin/env python
# coding=utf-8
"""
Debug script to test the training setup step by step.
"""

import os
import sys
import traceback
from pathlib import Path

# Add the scripts directory to the path
script_dir = Path(__file__).parent / "scripts"
sys.path.insert(0, str(script_dir))

# Add the src directory to the path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

def test_imports():
    """Test if we can import all required modules."""
    print("Testing imports...")
    
    try:
        from datasets import load_dataset
        print("✓ datasets")
    except Exception as e:
        print(f"✗ datasets: {e}")
        return False
    
    try:
        from alignment import (
            DataArguments,
            H4ArgumentParser,
            ModelArguments,
            SFTConfig,
            get_datasets,
            get_kbit_device_map,
            get_peft_config,
            get_quantization_config,
            get_tokenizer,
            is_adapter_model,
        )
        print("✓ alignment modules")
    except Exception as e:
        print(f"✗ alignment modules: {e}")
        return False
    
    try:
        from transformers import AutoTokenizer, set_seed
        print("✓ transformers")
    except Exception as e:
        print(f"✗ transformers: {e}")
        return False
    
    try:
        from trl import SFTTrainer
        print("✓ trl")
    except Exception as e:
        print(f"✗ trl: {e}")
        return False
    
    return True

def test_dataset_loading():
    """Test dataset loading and formatting."""
    print("\nTesting dataset loading...")
    
    try:
        from datasets import load_dataset
        
        # Load a small sample
        dataset = load_dataset("MothMalone/SLMS-KD-Benchmarks", "pubmedqa", split="train[:5]")
        print(f"✓ Loaded {len(dataset)} examples")
        
        # Test formatting function
        sys.path.insert(0, str(Path(__file__).parent / "scripts"))
        from run_sft_pubmedqa import format_pubmedqa_example
        
        # Test formatting on first example
        if len(dataset) > 0:
            example = dataset[0]
            print(f"Original example keys: {list(example.keys())}")
            
            formatted = format_pubmedqa_example(example)
            print(f"Formatted example keys: {list(formatted.keys())}")
            
            if 'messages' in formatted:
                print(f"Number of messages: {len(formatted['messages'])}")
                for i, msg in enumerate(formatted['messages']):
                    print(f"  Message {i}: role={msg['role']}, content_length={len(msg['content'])}")
            
            print("✓ Dataset formatting works")
            return True
        else:
            print("✗ No examples in dataset")
            return False
            
    except Exception as e:
        print(f"✗ Dataset loading failed: {e}")
        traceback.print_exc()
        return False

def test_argument_parsing():
    """Test argument parsing with our config."""
    print("\nTesting argument parsing...")
    
    try:
        # Set up sys.argv as if we're calling the script
        original_argv = sys.argv.copy()
        config_path = "recipes/llama3.2-1b/sft/config_pubmedqa.yaml"
        sys.argv = ["run_sft_pubmedqa.py", config_path]
        
        from alignment import H4ArgumentParser, ModelArguments, DataArguments, SFTConfig
        
        parser = H4ArgumentParser((ModelArguments, DataArguments, SFTConfig))
        model_args, data_args, training_args = parser.parse()
        
        print("✓ Argument parsing successful")
        print(f"  Model: {model_args.model_name_or_path}")
        print(f"  Dataset mixer: {data_args.dataset_mixer}")
        print(f"  Output dir: {training_args.output_dir}")
        
        # Restore argv
        sys.argv = original_argv
        return True
        
    except Exception as e:
        print(f"✗ Argument parsing failed: {e}")
        traceback.print_exc()
        sys.argv = original_argv
        return False

def main():
    """Main debug function."""
    print("=== PubMedQA Training Debug ===")
    
    # Change to the alignment-handbook directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    print(f"Working directory: {os.getcwd()}")
    
    # Test each component
    tests = [
        ("Imports", test_imports),
        ("Dataset Loading", test_dataset_loading),
        ("Argument Parsing", test_argument_parsing),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Testing: {test_name}")
        print('='*50)
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("SUMMARY")
    print('='*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Training should work.")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
