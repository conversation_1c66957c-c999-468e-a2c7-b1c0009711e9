from datasets import load_dataset
import sys

def format_pubmedqa(example):
    """Formats a single pubmedqa example into the required conversational format."""
    # Combine question and context for the user's prompt
    prompt = f"Question: {example['question']}\n\nContext: {example['context']}"
    # The 'long_answer' is the assistant's response
    answer = example['long_answer']
    
    return {
        "messages": [
            {"role": "user", "content": prompt},
            {"role": "assistant", "content": answer},
        ]
    }

def main():
    # Load the original dataset
    # We load the 'train' split first to inspect its columns
    dataset = load_dataset("MothMalone/SLMS-KD-Benchmarks", "pubmedqa")

    # --- Start of Correction ---
    # Get the list of all original columns from the 'train' split
    original_columns = dataset["train"].column_names
    
    # Check if required columns exist before processing
    required_cols = ["question", "context", "long_answer"]
    if not all(col in original_columns for col in required_cols):
        print(f"Error: Dataset is missing one of the required columns: {required_cols}", file=sys.stderr)
        print(f"Available columns: {original_columns}", file=sys.stderr)
        sys.exit(1)

    # Format the dataset.
    # The `remove_columns` will now correctly remove all the original columns,
    # leaving only the new 'messages' column.
    formatted_dataset = dataset.map(
        format_pubmedqa,
        batched=False, # Process one example at a time
        remove_columns=original_columns 
    )
    # --- End of Correction ---

    # Push to your Hugging Face Hub account
    # Replace 'YOUR_USERNAME' with your actual Hugging Face username
    # Make sure to create the repository on the Hub first, or use private=True
    hub_repo_name = "MothMalone/pubmedqa-llama-sft-formatted"
    formatted_dataset.push_to_hub(hub_repo_name)
    
    print(f"\nDataset successfully pushed to: https://huggingface.co/datasets/{hub_repo_name}")

if __name__ == "__main__":
    main()