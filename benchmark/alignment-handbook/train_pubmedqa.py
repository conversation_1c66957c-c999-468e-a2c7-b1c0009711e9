#!/usr/bin/env python
# coding=utf-8
"""
Complete training script for fine-tuning Llama 3.2:1B on PubMedQA dataset.
This script handles dataset loading, preprocessing, and training in one place.
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Add the scripts directory to the path so we can import our modules
script_dir = Path(__file__).parent / "scripts"
sys.path.insert(0, str(script_dir))

# Add the src directory to the path for alignment imports
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

def check_dataset_access():
    """Check if we can access the PubMedQA dataset."""
    try:
        from datasets import load_dataset
        logger = logging.getLogger(__name__)
        
        logger.info("Testing dataset access...")
        dataset = load_dataset("MothMalone/SLMS-KD-Benchmarks", "pubmedqa", split="train[:5]")
        logger.info(f"Successfully loaded sample data: {len(dataset)} examples")
        logger.info(f"Dataset columns: {dataset.column_names}")
        
        # Show a sample
        if len(dataset) > 0:
            sample = dataset[0]
            logger.info("Sample data structure:")
            for key, value in sample.items():
                if isinstance(value, str) and len(value) > 100:
                    logger.info(f"  {key}: {value[:100]}...")
                else:
                    logger.info(f"  {key}: {value}")
        
        return True
    except Exception as e:
        logger.error(f"Failed to access dataset: {e}")
        return False

def run_training(config_path: str, additional_args: list = None):
    """Run the training with the specified configuration."""
    logger = logging.getLogger(__name__)
    
    # Import the training script
    try:
        from run_sft_pubmedqa import main as train_main
        import sys
        
        # Set up sys.argv for the training script
        original_argv = sys.argv.copy()
        sys.argv = ["run_sft_pubmedqa.py", config_path]
        
        if additional_args:
            sys.argv.extend(additional_args)
        
        logger.info(f"Starting training with config: {config_path}")
        logger.info(f"Command line args: {sys.argv}")
        
        # Run the training
        train_main()
        
        # Restore original argv
        sys.argv = original_argv
        
        logger.info("Training completed successfully!")
        
    except Exception as e:
        import traceback
        logger.error(f"Training failed: {e}")
        logger.error(f"Full traceback:\n{traceback.format_exc()}")
        raise

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Train Llama 3.2:1B on PubMedQA dataset")
    parser.add_argument(
        "--config", 
        type=str, 
        default="recipes/llama3.2-1b/sft/config_pubmedqa.yaml",
        help="Path to the training configuration file"
    )
    parser.add_argument(
        "--test-dataset", 
        action="store_true",
        help="Test dataset access without training"
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        help="Override output directory"
    )
    parser.add_argument(
        "--num-epochs",
        type=int,
        help="Override number of training epochs"
    )
    parser.add_argument(
        "--learning-rate",
        type=float,
        help="Override learning rate"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        help="Override per-device batch size"
    )
    
    args = parser.parse_args()
    
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("=== PubMedQA Fine-tuning Script ===")
    logger.info(f"Config file: {args.config}")
    
    # Check if config file exists
    config_path = Path(args.config)
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        sys.exit(1)
    
    # Test dataset access if requested
    if args.test_dataset:
        logger.info("Testing dataset access...")
        if check_dataset_access():
            logger.info("Dataset access test passed!")
        else:
            logger.error("Dataset access test failed!")
            sys.exit(1)
        return
    
    # Prepare additional arguments for training
    additional_args = []

    # Always force minimal memory settings for safety
    additional_args.extend(["--per_device_train_batch_size", "1"])
    additional_args.extend(["--per_device_eval_batch_size", "1"])
    additional_args.extend(["--max_seq_length", "128"])
    additional_args.extend(["--gradient_accumulation_steps", "1"])
    additional_args.extend(["--logging_steps", "200"])
    additional_args.extend(["--eval_steps", "200"])
    additional_args.extend(["--save_steps", "200"])
    additional_args.extend(["--preprocessing_num_workers", "1"])
    additional_args.extend(["--fp16", "True"])
    additional_args.extend(["--bf16", "False"])
    additional_args.extend(["--gradient_checkpointing", "False"])
    additional_args.extend(["--load_in_4bit", "True"])
    additional_args.extend(["--bnb_4bit_quant_type", "nf4"])
    additional_args.extend(["--bnb_4bit_quant_storage", "uint8"])
    additional_args.extend(["--use_bnb_nested_quant", "False"])
    additional_args.extend(["--num_train_epochs", "1"])

    if args.output_dir:
        additional_args.extend(["--output_dir", args.output_dir])
    if args.num_epochs:
        additional_args.extend(["--num_train_epochs", str(args.num_epochs)])
    if args.learning_rate:
        additional_args.extend(["--learning_rate", str(args.learning_rate)])
    if args.batch_size:
        # User override, but still keep all other memory settings
        additional_args.extend(["--per_device_train_batch_size", str(args.batch_size)])
        additional_args.extend(["--per_device_eval_batch_size", str(args.batch_size)])
    
    # Check dataset access before training
    logger.info("Checking dataset access before training...")
    if not check_dataset_access():
        logger.error("Cannot access dataset. Please check your internet connection and dataset permissions.")
        sys.exit(1)
    
    # Run the training
    try:
        run_training(str(config_path), additional_args)
        logger.info("=== Training completed successfully! ===")
    except Exception as e:
        logger.error(f"=== Training failed: {e} ===")
        sys.exit(1)

if __name__ == "__main__":
    main()
