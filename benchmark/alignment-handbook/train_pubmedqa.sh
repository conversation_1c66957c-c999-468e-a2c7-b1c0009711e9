#!/bin/bash

# PubMedQA Fine-tuning Script for Llama 3.2:1B
# This script sets up the environment and runs the training

set -e  # Exit on any error

echo "=== PubMedQA Fine-tuning Setup ==="

# Check if we're in the right directory
if [ ! -f "train_pubmedqa.py" ]; then
    echo "Error: Please run this script from the benchmark/alignment-handbook directory"
    exit 1
fi

# Set default values
CONFIG_FILE="recipes/llama3.2-1b/sft/config_pubmedqa.yaml"
OUTPUT_DIR="./llama3.2-1b-pubmedqa-sft"
NUM_EPOCHS=3
LEARNING_RATE=2e-5
BATCH_SIZE=2
TEST_ONLY=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        --output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --epochs)
            NUM_EPOCHS="$2"
            shift 2
            ;;
        --lr)
            LEARNING_RATE="$2"
            shift 2
            ;;
        --batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --test-only)
            TEST_ONLY=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --config FILE         Configuration file (default: $CONFIG_FILE)"
            echo "  --output-dir DIR      Output directory (default: $OUTPUT_DIR)"
            echo "  --epochs N            Number of epochs (default: $NUM_EPOCHS)"
            echo "  --lr RATE             Learning rate (default: $LEARNING_RATE)"
            echo "  --batch-size N        Batch size (default: $BATCH_SIZE)"
            echo "  --test-only           Only test dataset access, don't train"
            echo "  --help                Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "Configuration:"
echo "  Config file: $CONFIG_FILE"
echo "  Output directory: $OUTPUT_DIR"
echo "  Epochs: $NUM_EPOCHS"
echo "  Learning rate: $LEARNING_RATE"
echo "  Batch size: $BATCH_SIZE"
echo "  Test only: $TEST_ONLY"

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Configuration file not found: $CONFIG_FILE"
    exit 1
fi

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Set up Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src:$(pwd)/scripts"

echo "=== Starting Training ==="

if [ "$TEST_ONLY" = true ]; then
    echo "Running dataset access test only..."
    python train_pubmedqa.py \
        --config "$CONFIG_FILE" \
        --test-dataset
else
    echo "Starting full training..."
    python train_pubmedqa.py \
        --config "$CONFIG_FILE" \
        --output-dir "$OUTPUT_DIR" \
        --num-epochs "$NUM_EPOCHS" \
        --learning-rate "$LEARNING_RATE" \
        --batch-size "$BATCH_SIZE"
fi

echo "=== Script completed ==="
