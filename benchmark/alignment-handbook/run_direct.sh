#!/bin/bash

# Direct training script that bypasses the wrapper
set -e

echo "=== Direct PubMedQA Training ==="

# Check if we're in the right directory
if [ ! -f "scripts/run_sft_pubmedqa.py" ]; then
    echo "Error: Please run this script from the benchmark/alignment-handbook directory"
    exit 1
fi

# Set up environment
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src:$(pwd)/scripts"

# Configuration
CONFIG_FILE="recipes/llama3.2-1b/sft/config_pubmedqa.yaml"
OUTPUT_DIR="./llama3.2-1b-pubmedqa-direct"

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        --output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --config FILE         Configuration file"
            echo "  --output-dir DIR      Output directory"
            echo "  --help                Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "Configuration file: $CONFIG_FILE"
echo "Output directory: $OUTPUT_DIR"

# Check config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Configuration file not found: $CONFIG_FILE"
    exit 1
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Run the training script directly
echo "Starting training..."
cd scripts
python run_sft_pubmedqa.py "../$CONFIG_FILE" --output_dir "../$OUTPUT_DIR"

echo "Training completed!"
