# PubMedQA Fine-tuning with Llama 3.2:1B

This directory contains the complete setup for fine-tuning Llama 3.2:1B on the PubMedQA dataset from the MothMalone/SLMS-KD-Benchmarks collection.

## Overview

The PubMedQA dataset is a medical question-answering dataset that contains:
- `pubid`: Publication ID
- `question`: Medical question
- `context`: Medical context with abstracts, labels, meshes, and predictions
- `long_answer`: Detailed answer to the question
- `final_decision`: Final decision (yes/no/maybe)

## Files Created

### Configuration
- `recipes/llama3.2-1b/sft/config_pubmedqa.yaml` - Training configuration optimized for Llama 3.2:1B and PubMedQA

### Scripts
- `scripts/run_sft_pubmedqa.py` - Specialized SFT script with PubMedQA preprocessing
- `scripts/preprocess_pubmedqa.py` - Standalone preprocessing script for PubMedQA
- `train_pubmedqa.py` - Complete training script with argument parsing
- `train_pubmedqa.sh` - Shell script for easy execution

### Documentation
- `README_PUBMEDQA.md` - This file

## Dataset Format

The script automatically converts PubMedQA data into conversational format:

**Input format:**
```json
{
  "question": "Does X cause Y?",
  "context": {"contexts": ["Abstract 1", "Abstract 2"], ...},
  "long_answer": "Based on the evidence..."
}
```

**Converted format:**
```json
{
  "messages": [
    {
      "role": "user", 
      "content": "Based on the following medical context, please answer the question.\n\nQuestion: Does X cause Y?\n\nContext: Abstract 1 Abstract 2\n\nPlease provide a detailed answer based on the medical evidence presented in the context."
    },
    {
      "role": "assistant",
      "content": "Based on the evidence..."
    }
  ]
}
```

## Quick Start

### 1. Test Dataset Access
```bash
cd benchmark/alignment-handbook
./train_pubmedqa.sh --test-only
```

### 2. Run Training with Default Settings
```bash
./train_pubmedqa.sh
```

### 3. Run Training with Custom Settings
```bash
./train_pubmedqa.sh \
    --epochs 5 \
    --lr 1e-5 \
    --batch-size 4 \
    --output-dir ./my-pubmedqa-model
```

## Configuration Details

The default configuration (`config_pubmedqa.yaml`) includes:

### Model Settings
- **Model**: `meta-llama/Llama-3.2-1B`
- **Precision**: `bfloat16`
- **Attention**: `flash_attention_2`

### Training Settings
- **Epochs**: 3
- **Learning Rate**: 2e-5
- **Batch Size**: 2 per device
- **Gradient Accumulation**: 4 steps
- **Max Sequence Length**: 2048
- **Scheduler**: Cosine with 10% warmup

### PEFT Settings (LoRA)
- **Enabled**: Yes (for memory efficiency)
- **Rank**: 16
- **Alpha**: 32
- **Dropout**: 0.05
- **Target Modules**: All linear layers

### Dataset Settings
- **Source**: `MothMalone/SLMS-KD-Benchmarks`
- **Config**: `pubmedqa`
- **Splits**: `train`, `test`

## Manual Usage

### Using Python Script Directly
```bash
cd benchmark/alignment-handbook
python train_pubmedqa.py --config recipes/llama3.2-1b/sft/config_pubmedqa.yaml
```

### Using the Specialized SFT Script
```bash
cd benchmark/alignment-handbook/scripts
python run_sft_pubmedqa.py ../recipes/llama3.2-1b/sft/config_pubmedqa.yaml
```

### Using YAML Configuration Only
```bash
cd benchmark/alignment-handbook/scripts
python run_sft.py ../recipes/llama3.2-1b/sft/config_pubmedqa.yaml
```

## Customization

### Modify Training Parameters
Edit `recipes/llama3.2-1b/sft/config_pubmedqa.yaml`:

```yaml
# Increase epochs
num_train_epochs: 5

# Adjust learning rate
learning_rate: 1.0e-05

# Change batch size
per_device_train_batch_size: 4

# Modify output directory
output_dir: ./my-custom-output
```

### Modify Data Preprocessing
Edit the `format_pubmedqa_example` function in:
- `scripts/run_sft_pubmedqa.py`
- `scripts/preprocess_pubmedqa.py`

### Add System Messages
Modify the prompt template in the formatting function to include system instructions:

```python
messages = [
    {"role": "system", "content": "You are a medical AI assistant..."},
    {"role": "user", "content": user_prompt},
    {"role": "assistant", "content": assistant_answer}
]
```

## Hardware Requirements

### Minimum Requirements
- **GPU**: 8GB VRAM (with LoRA)
- **RAM**: 16GB system RAM
- **Storage**: 10GB free space

### Recommended Requirements
- **GPU**: 16GB+ VRAM
- **RAM**: 32GB+ system RAM
- **Storage**: 50GB+ free space

## Troubleshooting

### Dataset Access Issues
```bash
# Test dataset access
./train_pubmedqa.sh --test-only

# Check Hugging Face authentication
huggingface-cli login
```

### Memory Issues
1. Reduce batch size in config:
   ```yaml
   per_device_train_batch_size: 1
   gradient_accumulation_steps: 8
   ```

2. Enable 4-bit quantization:
   ```yaml
   load_in_4bit: true
   ```

3. Reduce sequence length:
   ```yaml
   max_seq_length: 1024
   ```

### Import Errors
Make sure you're in the correct directory and have the required dependencies:
```bash
cd benchmark/alignment-handbook
pip install -r requirements.txt
```

## Output

The training will create:
- **Model files**: In the specified output directory
- **Logs**: Training logs and metrics
- **Checkpoints**: Intermediate model checkpoints
- **Metrics**: Training and evaluation metrics

## Next Steps

After training:
1. **Evaluate** the model on medical QA benchmarks
2. **Test** with sample medical questions
3. **Deploy** for inference
4. **Fine-tune** further if needed

## Support

For issues specific to this setup, check:
1. Dataset access and format
2. Configuration file syntax
3. Hardware compatibility
4. Dependency versions
