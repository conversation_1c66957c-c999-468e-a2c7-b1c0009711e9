#!/usr/bin/env python
# coding=utf-8
"""
Test script to validate the PubMedQA fine-tuning setup.
This script checks if all required files are in place and the configuration is valid.
"""

import os
import sys
import yaml
from pathlib import Path

def test_file_exists(file_path, description):
    """Test if a file exists."""
    if Path(file_path).exists():
        print(f"✓ {description}: {file_path}")
        return True
    else:
        print(f"✗ {description}: {file_path} (NOT FOUND)")
        return False

def test_yaml_config(config_path):
    """Test if YAML configuration is valid."""
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        print(f"✓ Configuration file is valid YAML: {config_path}")
        
        # Check required fields
        required_fields = [
            'model_name_or_path',
            'dataset_mixer',
            'dataset_configs',
            'output_dir'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in config:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"✗ Missing required fields in config: {missing_fields}")
            return False
        else:
            print("✓ All required configuration fields present")
            
        # Print key configuration values
        print(f"  - Model: {config.get('model_name_or_path')}")
        print(f"  - Dataset: {list(config.get('dataset_mixer', {}).keys())}")
        print(f"  - Config: {config.get('dataset_configs')}")
        print(f"  - Output: {config.get('output_dir')}")
        
        return True
        
    except yaml.YAMLError as e:
        print(f"✗ Invalid YAML in {config_path}: {e}")
        return False
    except Exception as e:
        print(f"✗ Error reading {config_path}: {e}")
        return False

def test_python_imports():
    """Test if we can import required modules (if available)."""
    print("\nTesting Python imports (optional):")
    
    modules_to_test = [
        ('yaml', 'PyYAML'),
        ('datasets', 'Hugging Face Datasets'),
        ('transformers', 'Hugging Face Transformers'),
        ('torch', 'PyTorch'),
        ('trl', 'TRL'),
    ]
    
    available_modules = []
    missing_modules = []
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {description} ({module_name})")
            available_modules.append(module_name)
        except ImportError:
            print(f"✗ {description} ({module_name}) - not installed")
            missing_modules.append(module_name)
    
    return available_modules, missing_modules

def main():
    """Main test function."""
    print("=== PubMedQA Fine-tuning Setup Validation ===\n")
    
    # Change to the alignment-handbook directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print(f"Working directory: {os.getcwd()}\n")
    
    # Test file existence
    print("Testing file existence:")
    files_to_test = [
        ("recipes/llama3.2-1b/sft/config_pubmedqa.yaml", "Configuration file"),
        ("scripts/run_sft_pubmedqa.py", "Specialized SFT script"),
        ("scripts/preprocess_pubmedqa.py", "Preprocessing script"),
        ("train_pubmedqa.py", "Main training script"),
        ("train_pubmedqa.sh", "Shell training script"),
        ("README_PUBMEDQA.md", "Documentation"),
    ]
    
    all_files_exist = True
    for file_path, description in files_to_test:
        if not test_file_exists(file_path, description):
            all_files_exist = False
    
    print()
    
    # Test configuration file
    print("Testing configuration:")
    config_path = "recipes/llama3.2-1b/sft/config_pubmedqa.yaml"
    config_valid = test_yaml_config(config_path)
    
    # Test Python imports
    available_modules, missing_modules = test_python_imports()
    
    # Summary
    print("\n=== Summary ===")
    
    if all_files_exist:
        print("✓ All required files are present")
    else:
        print("✗ Some required files are missing")
    
    if config_valid:
        print("✓ Configuration file is valid")
    else:
        print("✗ Configuration file has issues")
    
    if missing_modules:
        print(f"⚠ Missing Python modules: {missing_modules}")
        print("  Install with: pip install alignment-handbook[dev]")
        print("  Or install individually:")
        for module in missing_modules:
            if module == 'yaml':
                print(f"    pip install PyYAML")
            else:
                print(f"    pip install {module}")
    else:
        print("✓ All required Python modules are available")
    
    # Overall status
    setup_ready = all_files_exist and config_valid
    
    if setup_ready:
        print("\n🎉 Setup validation PASSED! You can proceed with training.")
        if missing_modules:
            print("   Note: Install missing modules first for full functionality.")
    else:
        print("\n❌ Setup validation FAILED! Please fix the issues above.")
    
    return 0 if setup_ready else 1

if __name__ == "__main__":
    sys.exit(main())
