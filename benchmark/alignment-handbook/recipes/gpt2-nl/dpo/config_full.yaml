# Model arguments
model_name_or_path: <PERSON><PERSON><PERSON><PERSON>/gpt2-sft-dutch
model_revision: main
torch_dtype: bfloat16

# Data training arguments
# For definitions, see: src/h4/training/config.py
dataset_mixer:
  <PERSON><PERSON><PERSON><PERSON>/ultra_feedback_dutch: 1.0
dataset_splits:
- train_prefs
- test_prefs
preprocessing_num_workers: 12

# DPOTrainer arguments
bf16: true
beta: 0.1
do_eval: true
eval_strategy: steps
eval_steps: 100
gradient_accumulation_steps: 8
gradient_checkpointing: true
gradient_checkpointing_kwargs:
  use_reentrant: False
hub_model_id: gpt2-dpo-dutch
learning_rate: 5.0e-7
log_level: info
logging_steps: 10
lr_scheduler_type: cosine
max_length: 1024
max_prompt_length: 512
num_train_epochs: 1
optim: adamw_torch
output_dir: data/gpt2-dpo-dutch
per_device_train_batch_size: 8
per_device_eval_batch_size: 8
push_to_hub: true
save_strategy: "steps"
save_steps: 100
save_total_limit: 1
seed: 42
warmup_ratio: 0.1
report_to:
- wandb
