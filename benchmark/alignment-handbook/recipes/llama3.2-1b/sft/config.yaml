model_name_or_path: "meta-llama/Llama-3.2-1B"
torch_dtype: "bfloat16" # Use bfloat16 for training stability on modern GPUs.


# This combination is essential for fitting the model into 16GB VRAM.
load_in_4bit: true          # CRITICAL: Loads the base model in 4-bit precision. The biggest memory saver.
bnb_4bit_quant_type: "nf4"  # A high-quality quantization type.

use_peft: true              # CRITICAL: Enables Parameter-Efficient Fine-Tuning.
lora_r: 16                  # Rank of the LoRA adapters. 16 is a good balance. Lower to 8 if you still face memory issues.
lora_alpha: 32              # Standard practice is to set alpha = 2 * r.
lora_dropout: 0.05          # Regularization to prevent LoRA adapters from overfitting.
lora_target_modules:        # Specifies which parts of the model to attach LoRA adapters to. These are standard for Llama models.
  - q_proj
  - k_proj
  - v_proj
  - o_proj
  - gate_proj
  - up_proj
  - down_proj

# ===================================================================
# # 2. SFT Trainer Configuration
# # These settings control the training loop itself.
# ===================================================================
# --- VRAM CRITICAL: Training Loop Settings ---
per_device_train_batch_size: 1    
per_device_eval_batch_size: 1     
gradient_accumulation_steps: 16   
gradient_checkpointing: true      
max_seq_length: 1024              
packing: false                    # RECOMMENDED: Disabling packing provides a more stable memory footprint, which is safer on low-VRAM setups.

# --- General Hyperparameters ---
num_train_epochs: 3               # Standard number of training epochs for fine-tuning.
learning_rate: 2.0e-05            
lr_scheduler_type: "cosine"       # Gradually decreases the learning rate, which helps with stable convergence.
warmup_ratio: 0.1                 
fp16: true                        # Use 16-bit mixed-precision training.
bf16: false                       # Ensure only one precision type is enabled. fp16 has broader compatibility.

# --- Logging, Saving, and Evaluation ---
output_dir: "./results-llama3.2-1b-sft" # Directory to save checkpoints and final model.
overwrite_output_dir: true
log_level: "info"
logging_strategy: "steps"        
logging_steps: 25                 
evaluation_strategy: "epoch"      
save_strategy: "epoch"            
save_total_limit: 1               
report_to: None                  
do_eval: true


# --- Hugging Face Hub Integration ---
push_to_hub: true                 # Set to true to upload the model to the Hub.
hub_model_id: "MothMalone/Llama-3.2-1B-SFT-casehold" # The name of the repository on the Hub. CHANGE THIS FOR EACH RUN.