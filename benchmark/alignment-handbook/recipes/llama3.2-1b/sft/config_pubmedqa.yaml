# Model arguments
model_name_or_path: meta-llama/Llama-3.2-1B
model_revision: main
torch_dtype: bfloat16
attn_implementation: flash_attention_2

# Data training arguments
chat_template: "{% for message in messages %}\n{% if message['role'] == 'user' %}\n{{ '<|user|>\n' + message['content'] + eos_token }}\n{% elif message['role'] == 'system' %}\n{{ '<|system|>\n' + message['content'] + eos_token }}\n{% elif message['role'] == 'assistant' %}\n{{ '<|assistant|>\n'  + message['content'] + eos_token }}\n{% endif %}\n{% if loop.last and add_generation_prompt %}\n{{ '<|assistant|>' }}\n{% endif %}\n{% endfor %}"
dataset_mixer:
  MothMalone/SLMS-KD-Benchmarks: 1.0
dataset_configs:
- pubmedqa
dataset_splits:
- train
- test
preprocessing_num_workers: 4
auto_insert_empty_system_msg: true

# SFT trainer config
bf16: true
do_eval: true
eval_strategy: steps
eval_steps: 100
gradient_accumulation_steps: 4
gradient_checkpointing: true
gradient_checkpointing_kwargs:
  use_reentrant: False
hub_model_id: MothMalone/Llama-3.2-1B-PubMedQA-SFT
learning_rate: 2.0e-05
log_level: info
logging_steps: 5
logging_first_step: true
lr_scheduler_type: cosine
max_steps: -1
num_train_epochs: 3
output_dir: ./llama3.2-1b-pubmedqa-sft
overwrite_output_dir: true
per_device_eval_batch_size: 2
per_device_train_batch_size: 2
push_to_hub: false
remove_unused_columns: true
report_to:
- tensorboard
save_strategy: steps
save_steps: 100
save_total_limit: 1
seed: 42
warmup_ratio: 0.1
max_seq_length: 2048

# PEFT config for efficient training on smaller hardware
use_peft: true
lora_r: 16
lora_alpha: 32
lora_dropout: 0.05
lora_target_modules:
- q_proj
- k_proj
- v_proj
- o_proj
- gate_proj
- up_proj
- down_proj

# Quantization for memory efficiency (if needed)
load_in_4bit: false
