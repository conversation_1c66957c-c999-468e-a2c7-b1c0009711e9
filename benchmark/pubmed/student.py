from transformers import AutoTokenizer, AutoModelForCausalLM
import datasets
import dotenv
import os
import warnings
import json
import re
from pydantic import BaseModel, Field, ValidationError
from sklearn.metrics import precision_score, recall_score, f1_score, accuracy_score, confusion_matrix
import numpy as np
import pandas as pd
from typing import Literal
import torch

warnings.filterwarnings("ignore")

# Define the model for the answer (same as teacher)
class PubmedqaAnswer(BaseModel):
    answer: Literal["yes", "no", "maybe"]

# Load environment variables
dotenv.load_dotenv('../../.env')

# Load dataset (same as teacher)
ds = datasets.load_dataset("MothMalone/SLMS-KD-Benchmarks", "pubmedqa")
data = ds['train']
print(len(data), "cases loaded from the dataset.")

# Dataset context (same as teacher)
DATASET_CONTEXT = """
PubMedQA is a dataset and a task that involves Question Answering (QA) using scientific literature from PubMed, which is a free resource that contains millions of articles related to life sciences and biomedical research. PubMedQA specifically focuses on using abstracts and passages from PubMed articles to answer medical and scientific questions.
"""

# Define the student model ID
STUDENT_MODEL_ID = "MothMalone/Llama3.2-3B-to-1B-PubmedQA-Distilled"

# Load the student model and tokenizer
print("Loading student model...")
tokenizer = AutoTokenizer.from_pretrained(STUDENT_MODEL_ID)
model = AutoModelForCausalLM.from_pretrained(
    STUDENT_MODEL_ID,
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto" if torch.cuda.is_available() else None
)

# Set pad token if not exists
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

print(f"Model loaded. Device: {model.device if hasattr(model, 'device') else 'CPU'}")

# Define label encoding function (same as teacher)
label_mapping = {
    "yes": 1,
    "no": 0,
    "maybe": 2
}

def encode_labels(label):
    return label_mapping.get(label, -1)

def extract_answer_from_response(response_text):
    """
    Extract structured answer from model response using multiple strategies
    """
    response_lower = response_text.lower().strip()
    
    # Strategy 1: Look for JSON-like structure
    json_patterns = [
        r'"answer":\s*"(yes|no|maybe)"',
        r"'answer':\s*'(yes|no|maybe)'",
        r'answer"?\s*:\s*"?(yes|no|maybe)"?',
    ]
    
    for pattern in json_patterns:
        match = re.search(pattern, response_lower)
        if match:
            return match.group(1)
    
    # Strategy 2: Look for explicit final decision
    decision_patterns = [
        r'final[_\s]?decision[:\s]+(yes|no|maybe)',
        r'answer[:\s]+(yes|no|maybe)',
        r'decision[:\s]+(yes|no|maybe)',
        r'conclusion[:\s]+(yes|no|maybe)',
    ]
    
    for pattern in decision_patterns:
        match = re.search(pattern, response_lower)
        if match:
            return match.group(1)
    
    # Strategy 3: Look for the answer at the end of response
    end_patterns = [
        r'\b(yes|no|maybe)\s*\.?\s*$',
        r'therefore[,\s]+(yes|no|maybe)',
        r'thus[,\s]+(yes|no|maybe)',
    ]
    
    for pattern in end_patterns:
        match = re.search(pattern, response_lower)
        if match:
            return match.group(1)
    
    # Strategy 4: Count occurrences and take the most frequent
    yes_count = len(re.findall(r'\byes\b', response_lower))
    no_count = len(re.findall(r'\bno\b', response_lower))
    maybe_count = len(re.findall(r'\bmaybe\b', response_lower))
    
    counts = {'yes': yes_count, 'no': no_count, 'maybe': maybe_count}
    if max(counts.values()) > 0:
        return max(counts, key=counts.get)
    
    return None

def predict_answer_student(case_data):
    """
    Predict answer using the student model with the same prompt as teacher
    """
    try:
        # Use the exact same prompt structure as teacher model
        prompt = f"""
{DATASET_CONTEXT}
Given the following question from the PubMedQA dataset: 
question : {case_data['question']}, 
with these data:
context : {case_data['context']}, 
long_answer {case_data['long_answer']}, 
Please provide the final decision based on these data and provide the final_decision. It is either
"yes" or "no" or "maybe".

Please respond in JSON format: {{"answer": "yes/no/maybe"}}
"""

        # Tokenize input
        inputs = tokenizer.encode(prompt, return_tensors="pt", truncation=True, max_length=2048)
        
        # Move to same device as model
        if hasattr(model, 'device'):
            inputs = inputs.to(model.device)
        
        # Generate response with same parameters as teacher (temperature=1, top_p=1)
        with torch.no_grad():
            outputs = model.generate(
                inputs,
                max_new_tokens=256,  # Reduced since we only need a short answer
                temperature=1.0,     # Same as teacher
                top_p=1.0,          # Same as teacher
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
            )
        
        # Decode response
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract only the generated part (after the prompt)
        prompt_length = len(tokenizer.decode(inputs[0], skip_special_tokens=True))
        generated_text = response[prompt_length:].strip()
        
        # Extract structured answer
        predicted_answer = extract_answer_from_response(generated_text)
        
        if predicted_answer:
            print(f"Predicted answer: {predicted_answer}")
            return predicted_answer
        else:
            print(f"Could not extract valid answer from: {generated_text[:100]}...")
            return None
            
    except Exception as e:
        print(f"Error in prediction: {e}")
        return None

def process_case_student(case_data):
    """
    Process a single case with the student model
    """
    predicted_answer = predict_answer_student(case_data)
    
    if predicted_answer not in {"yes", "no", "maybe"}:
        print(f"Invalid answer received: {predicted_answer}. Skipping this case.")
        return case_data['final_decision'], predicted_answer
    
    actual_answer = case_data['final_decision']
    return actual_answer, predicted_answer

# Initialize metrics and results (same as teacher)
y_true = []
y_pred = []
invalid_cases = 0
metrics_df = pd.DataFrame(columns=["Accuracy", "Precision", "Recall", "F1 Score", "Macro F1 Score", "Invalid Cases"])

# Process the same number of cases as teacher for fair comparison
print("Starting inference on student model...")

# Loop through dataset and process each case (same logic as teacher)
for i, case_data in enumerate(data):
    actual_answer, predicted_answer = process_case_student(case_data)
    
    if predicted_answer is not None:
        # Encode both actual and predicted answers
        y_true.append(encode_labels(actual_answer))
        y_pred.append(encode_labels(predicted_answer))
    else:
        invalid_cases += 1

    # Calculate metrics periodically (every 10th case or at the end) - same as teacher
    if (i + 1) % 10 == 0 or (i + 1) == len(data):
        # Convert lists to numpy arrays for metric calculations
        y_true_array = np.array(y_true, dtype=int)
        y_pred_array = np.array(y_pred, dtype=int)

        # Calculate metrics (same as teacher)
        accuracy = accuracy_score(y_true_array, y_pred_array)
        precision = precision_score(y_true_array, y_pred_array, average='weighted', zero_division=0)
        recall = recall_score(y_true_array, y_pred_array, average='weighted', zero_division=0)
        f1 = f1_score(y_true_array, y_pred_array, average='weighted', zero_division=0)
        f1_macro = f1_score(y_true_array, y_pred_array, average='macro', zero_division=0)
        conf_matrix = confusion_matrix(y_true_array, y_pred_array)

        # Log metrics to a DataFrame
        metrics_row = pd.DataFrame([{
            "Accuracy": round(accuracy * 100, 3),
            "Precision": round(precision * 100, 3),
            "Recall": round(recall * 100, 3),
            "F1 Score": round(f1 * 100, 3),
            "Macro F1 Score": round(f1_macro * 100, 3),   
            "Invalid Cases": invalid_cases
        }])
        metrics_df = pd.concat([metrics_df, metrics_row], ignore_index=True)

        # Save metrics to CSV (different filename to avoid overwriting teacher results)
        metrics_df.to_csv("student_metrics_log.csv", index=False, mode='w')
        print(f"Logged student model metrics after {i + 1} cases.")

# Final metrics after processing all cases (same as teacher)
y_true_array = np.array(y_true, dtype=int)
y_pred_array = np.array(y_pred, dtype=int)

accuracy = accuracy_score(y_true_array, y_pred_array)
precision = precision_score(y_true_array, y_pred_array, average='weighted', zero_division=0)
recall = recall_score(y_true_array, y_pred_array, average='weighted', zero_division=0)
f1 = f1_score(y_true_array, y_pred_array, average='weighted', zero_division=0)
f1_macro = f1_score(y_true_array, y_pred_array, average='macro', zero_division=0)
conf_matrix = confusion_matrix(y_true_array, y_pred_array)

# Print final results
print("\n" + "="*50)
print("STUDENT MODEL FINAL RESULTS")
print("="*50)
print(f"Final Accuracy: {accuracy * 100:.2f}%")
print(f"Final Precision: {precision * 100:.2f}%")
print(f"Final Recall: {recall * 100:.2f}%")
print(f"Final F1 Score: {f1 * 100:.2f}%")
print(f"Final Macro F1 Score: {f1_macro * 100:.2f}%")
print("Confusion Matrix:")
print(conf_matrix)
print(f"Invalid Cases: {invalid_cases}")

# Save final results to a separate file for comparison
final_results = {
    "model": "student",
    "model_id": STUDENT_MODEL_ID,
    "accuracy": round(accuracy * 100, 3),
    "precision": round(precision * 100, 3),
    "recall": round(recall * 100, 3),
    "f1_score": round(f1 * 100, 3),
    "macro_f1_score": round(f1_macro * 100, 3),
    "invalid_cases": invalid_cases,
    "total_cases": len(data),
    "confusion_matrix": conf_matrix.tolist()
}

with open("student_final_results.json", "w") as f:
    json.dump(final_results, f, indent=2)

print(f"\nResults saved to student_final_results.json and student_metrics_log.csv")