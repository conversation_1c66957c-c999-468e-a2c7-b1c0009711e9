{"os": "Linux-6.8.0-57-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.12", "startedAt": "2025-06-30T10:43:23.760275Z", "program": "/drive1/nammt/KD-SLM/benchmark/pubmed/test.py", "codePath": "benchmark/pubmed/test.py", "git": {"remote": "**************:iSE-UET-VNU/KD-SLM.git", "commit": "8ad95f3fb30cae292404aa565d2118886903660f"}, "email": "<EMAIL>", "root": "/drive1/nammt/KD-SLM/benchmark/pubmed", "host": "fit-Precision-7920-Tower", "executable": "/home/<USER>/KD-SLM/venv/bin/python3", "codePathLocal": "test.py", "cpu_count": 8, "cpu_count_logical": 8, "gpu": "NVIDIA RTX A4000", "gpu_count": 1, "disk": {"/": {"total": "1869415788544", "used": "1764969054208"}}, "memory": {"total": "33290887168"}, "cpu": {"count": 8, "countLogical": 8}, "gpu_nvidia": [{"name": "NVIDIA RTX A4000", "memoryTotal": "17171480576", "cudaCores": 6144, "architecture": "Ampere", "uuid": "GPU-bf97020e-cabc-d5b4-47de-c4df0eaf5ee2"}], "cudaVersion": "12.2"}