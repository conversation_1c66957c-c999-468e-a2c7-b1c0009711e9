__cached__setup_devices:
    value: cuda:0
_n_gpu:
    value: 1
_wandb:
    value:
        cli_version: 0.20.1
        m: []
        python_version: 3.10.12
        t:
            "1":
                - 1
                - 5
                - 11
                - 30
                - 41
                - 49
                - 51
                - 53
                - 71
                - 83
                - 84
                - 95
                - 98
            "2":
                - 1
                - 5
                - 11
                - 30
                - 41
                - 49
                - 51
                - 53
                - 71
                - 83
                - 84
                - 95
                - 98
            "3":
                - 13
                - 14
                - 16
                - 55
            "4": 3.10.12
            "5": 0.20.1
            "6": 4.53.0
            "8":
                - 2
            "12": 0.20.1
            "13": linux-x86_64
accelerator_config:
    value:
        dispatch_batches: null
        even_batches: true
        gradient_accumulation_kwargs: null
        non_blocking: false
        split_batches: false
        use_configured_state: false
        use_seedable_sampler: true
adafactor:
    value: false
adam_beta1:
    value: 0.9
adam_beta2:
    value: 0.999
adam_epsilon:
    value: 1e-08
auto_find_batch_size:
    value: false
average_tokens_across_devices:
    value: false
batch_eval_metrics:
    value: false
bf16:
    value: false
bf16_full_eval:
    value: false
data_seed:
    value: null
dataloader_drop_last:
    value: false
dataloader_num_workers:
    value: 0
dataloader_persistent_workers:
    value: false
dataloader_pin_memory:
    value: true
dataloader_prefetch_factor:
    value: null
dataset_config_name:
    value: pubmedqa
dataset_name:
    value: MothMalone/SLMS-KD-Benchmarks
ddp_backend:
    value: null
ddp_broadcast_buffers:
    value: null
ddp_bucket_cap_mb:
    value: null
ddp_find_unused_parameters:
    value: null
ddp_timeout:
    value: 1800
debug:
    value: []
deepspeed:
    value: null
deepspeed_plugin:
    value: null
disable_tqdm:
    value: false
distributed_state:
    value: |
        Distributed environment: NO
        Num processes: 1
        Process index: 0
        Local process index: 0
        Device: cuda
do_eval:
    value: false
do_predict:
    value: false
do_train:
    value: false
eval_accumulation_steps:
    value: null
eval_delay:
    value: 0
eval_do_concat_batches:
    value: true
eval_on_start:
    value: false
eval_steps:
    value: null
eval_strategy:
    value: "NO"
eval_use_gather_object:
    value: false
fp16:
    value: false
fp16_backend:
    value: auto
fp16_full_eval:
    value: false
fp16_opt_level:
    value: O1
fsdp:
    value: []
fsdp_config:
    value:
        min_num_params: 0
        xla: false
        xla_fsdp_grad_ckpt: false
        xla_fsdp_v2: false
fsdp_min_num_params:
    value: 0
fsdp_transformer_layer_cls_to_wrap:
    value: null
full_determinism:
    value: false
gradient_accumulation_steps:
    value: 1
gradient_checkpointing:
    value: false
gradient_checkpointing_kwargs:
    value: null
greater_is_better:
    value: null
group_by_length:
    value: false
half_precision_backend:
    value: auto
hub_always_push:
    value: false
hub_model_id:
    value: null
hub_private_repo:
    value: null
hub_revision:
    value: null
hub_strategy:
    value: EVERY_SAVE
hub_token:
    value: null
ignore_data_skip:
    value: false
include_for_metrics:
    value: []
include_inputs_for_metrics:
    value: false
include_num_input_tokens_seen:
    value: false
include_tokens_per_second:
    value: false
jit_mode_eval:
    value: false
label_names:
    value: null
label_smoothing_factor:
    value: 0
learning_rate:
    value: 5e-05
length_column_name:
    value: length
liger_kernel_config:
    value: null
load_best_model_at_end:
    value: false
local_rank:
    value: 0
log_level:
    value: passive
log_level_replica:
    value: warning
log_on_each_node:
    value: true
logging_dir:
    value: trainer_output/runs/Jun30_18-26-10_fit-Precision-7920-Tower
logging_first_step:
    value: false
logging_nan_inf_filter:
    value: true
logging_steps:
    value: 500
logging_strategy:
    value: STEPS
lora_alpha:
    value: 32
lora_dropout:
    value: 0.05
lora_r:
    value: 16
lr_scheduler_type:
    value: LINEAR
max_grad_norm:
    value: 1
max_seq_length:
    value: 2048
max_steps:
    value: -1
metric_for_best_model:
    value: null
model_name_or_path:
    value: meta-llama/Llama-3.2-1B
mp_parameters:
    value: ""
neftune_noise_alpha:
    value: null
no_cuda:
    value: false
num_train_epochs:
    value: 3
optim:
    value: ADAMW_TORCH
optim_args:
    value: null
optim_target_modules:
    value: null
output_dir:
    value: trainer_output
overwrite_output_dir:
    value: false
past_index:
    value: -1
per_device_eval_batch_size:
    value: 8
per_device_train_batch_size:
    value: 8
per_gpu_eval_batch_size:
    value: null
per_gpu_train_batch_size:
    value: null
prediction_loss_only:
    value: false
push_to_hub:
    value: false
push_to_hub_model_id:
    value: null
push_to_hub_organization:
    value: null
push_to_hub_token:
    value: null
ray_scope:
    value: last
remove_unused_columns:
    value: true
report_to:
    value:
        - wandb
restore_callback_states_from_checkpoint:
    value: false
resume_from_checkpoint:
    value: null
run_name:
    value: trainer_output
save_on_each_node:
    value: false
save_only_model:
    value: false
save_safetensors:
    value: true
save_steps:
    value: 500
save_strategy:
    value: STEPS
save_total_limit:
    value: null
seed:
    value: 42
skip_memory_metrics:
    value: true
tf32:
    value: null
torch_compile:
    value: false
torch_compile_backend:
    value: null
torch_compile_mode:
    value: null
torch_dtype:
    value: auto
torch_empty_cache_steps:
    value: null
torchdynamo:
    value: null
tpu_metrics_debug:
    value: false
tpu_num_cores:
    value: null
use_cpu:
    value: false
use_ipex:
    value: false
use_legacy_prediction_loop:
    value: false
use_liger_kernel:
    value: false
use_mps_device:
    value: false
warmup_ratio:
    value: 0
warmup_steps:
    value: 0
weight_decay:
    value: 0
