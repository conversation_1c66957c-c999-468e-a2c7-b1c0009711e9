peft==0.15.2
msgpack==1.1.1
referencing==0.36.2
fastrlock==0.8.3
certifi==2025.6.15
scipy==1.15.3
grpcio-tools==1.73.0
Pygments==2.19.1
promise==2.3
parso==0.8.4
click==8.1.8
tornado==6.5.1
bitsandbytes==0.46.0
pandas==2.3.0
pydantic==2.11.5
huggingface-hub==0.33.0
scikit-learn==1.7.0
python-json-logger==3.3.0
uvicorn==0.34.3
distro==1.9.0
threadpoolctl==3.6.0
dnspython==2.7.0
einops==0.8.1
aiosignal==1.3.2
genson==1.3.0
propcache==0.3.1
xxhash==3.5.0
pyarrow==20.0.0
vllm==0.9.1
ninja==********
email_validator==2.2.0
triton==3.3.0
ray==2.47.1
typing_extensions==4.14.0
pathtools==0.1.2
docker-pycreds==0.4.0
nvidia-cusparselt-cu12==0.6.3
numpy==2.2.6
opentelemetry-exporter-otlp-proto-http==1.34.1
aiohappyeyeballs==2.6.1
tzdata==2025.2
watchfiles==1.0.5
shellingham==1.5.4
sympy==1.14.0
toml==0.10.2
traitlets==5.14.3
h11==0.16.0
matplotlib-inline==0.1.7
blake3==1.0.5
multiprocess==0.70.16
opentelemetry-sdk==1.34.1
grpcio==1.73.0
prometheus-fastapi-instrumentator==7.1.0
Jinja2==3.1.6
types-toml==0.10.8.20240310
opentelemetry-proto==1.34.1
instructor==1.8.3
pyzmq==26.4.0
typer==0.16.0
MarkupSafe==3.0.2
h2==4.2.0
jupyter_core==5.8.1
decorator==5.2.1
exceptiongroup==1.3.0
filelock==3.18.0
GitPython==3.1.44
partial-json-parser==*******.post6
depyf==0.18.0
urllib3==2.5.0
diskcache==5.6.3
fastapi-cli==0.0.7
python-dotenv==1.1.0
setuptools==80.9.0
multidict==6.4.4
ipykernel==6.29.5
nvidia-cufile-cu12==********
gitdb==4.0.12
subprocess32==3.5.4
types-certifi==2021.10.8.3
jedi==0.19.2
llguidance==0.7.30
networkx==3.4.2
rpds-py==0.25.1
ruff==0.12.0
prometheus_client==0.22.1
setproctitle==1.3.6
trl==0.18.1
nvidia-nccl-cu12==2.26.2
pydantic_core==2.33.2
wheel==0.45.1
accelerate==1.7.0
configparser==7.2.0
nvidia-nvjitlink-cu12==12.6.85
nvidia-cuda-runtime-cu12==12.6.77
transformers==4.52.4
rich==13.9.4
tqdm==4.67.1
nest-asyncio==1.6.0
grpclib==0.4.7
mdurl==0.1.2
fsspec==2025.3.0
googleapis-common-protos==1.70.0
httpx==0.28.1
nvidia-cusolver-cu12==********
annotated-types==0.7.0
xformers==0.0.30
smmap==5.0.2
jiter==0.8.2
aiohttp==3.12.11
importlib_metadata==8.7.0
dill==0.3.8
lark==1.2.2
ptyprocess==0.7.0
psutil==7.0.0
safetensors==0.5.3
hpack==4.1.0
nvidia-cuda-cupti-cu12==12.6.80
requests==2.32.4
torch==2.7.0
opencv-python-headless==*********
frozenlist==1.6.2
inquirerpy==0.3.4
PyYAML==6.0.2
py-cpuinfo==9.0.0
opentelemetry-api==1.34.1
cupy-cuda12x==13.4.1
nvidia-nvtx-cu12==12.6.77
cachetools==6.1.0
astor==0.8.1
mpmath==1.3.0
lm-format-enforcer==0.10.11
markdown-it-py==3.0.0
sentencepiece==0.2.0
packaging==25.0
typing-inspection==0.4.1
charset-normalizer==3.4.2
wcwidth==0.2.13
stack-data==0.6.3
hyperframe==6.1.0
torchvision==0.22.0
sniffio==1.3.1
datasets==3.6.0
cloudpickle==3.1.1
compressed-tensors==0.10.1
mistral_common==1.6.2
synchronicity==0.9.13
opentelemetry-semantic-conventions==0.55b1
tokenizers==0.21.1
interegular==0.3.3
xgrammar==0.1.19
six==1.17.0
pexpect==4.9.0
ipython==8.37.0
wandb==0.20.1
nvidia-cufft-cu12==********
pillow==11.2.1
pytz==2025.2
sentry-sdk==2.31.0
nvidia-cublas-cu12==********
nvidia-cudnn-cu12==********
outlines==1.0.2
platformdirs==4.3.8
llvmlite==0.44.0
iso3166==2.1.1
nvidia-cusparse-cu12==********
httptools==0.6.4
yarl==1.20.0
python-dateutil==2.9.0.post0
pure_eval==0.2.3
pip==25.1.1
openai==1.86.0
opentelemetry-exporter-otlp-proto-grpc==1.34.1
nvidia-curand-cu12==*********
asttokens==3.0.0
debugpy==1.8.14
numba==0.61.2
pfzy==0.3.4
joblib==1.5.1
anyio==4.9.0
jsonschema==4.24.0
zipp==3.23.0
opentelemetry-exporter-otlp==1.34.1
opentelemetry-exporter-otlp-proto-common==1.34.1
attrs==25.3.0
hf-xet==1.1.5
uvloop==0.21.0
prompt_toolkit==3.0.51
tenacity==9.1.2
jsonschema-specifications==2025.4.1
executing==2.2.0
docstring_parser==0.16
opentelemetry-semantic-conventions-ai==0.4.9
msgspec==0.19.0
tiktoken==0.9.0
torchaudio==2.7.0
async-timeout==5.0.1
comm==0.2.2
jupyter_client==8.6.3
outlines_core==0.1.26
nvidia-cuda-nvrtc-cu12==12.6.77
httpcore==1.0.9
regex==2024.11.6
websockets==15.0.1
starlette==0.46.2
rich-toolkit==0.14.7
groq==0.28.0
modal==1.0.3
sigtools==4.0.1
idna==3.10
gguf==0.17.1
pycountry==24.6.1
airportsdata==20250622
python-multipart==0.0.20
fastapi==0.115.12
protobuf==5.29.5
shortuuid==1.0.13
