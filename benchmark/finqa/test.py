import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

# --- SETUP ---
# The Hugging Face repository ID for your NEW FinQA model
model_id = "MothMalone/Llama3.2-3B-to-1B-FinQA-Distilled"
device = "cuda" if torch.cuda.is_available() else "cpu"

print(f"Loading model: {model_id}")
print(f"Using device: {device}")

# --- LOAD MODEL AND TOKENIZER ---
try:
    model = AutoModelForCausalLM.from_pretrained(
        model_id,
        torch_dtype=torch.bfloat16
    ).to(device)
    tokenizer = AutoTokenizer.from_pretrained(model_id)
    print("\n--- Model and Tokenizer Loaded Successfully ---")
except Exception as e:
    print(f"\n--- ERROR ---")
    print(f"Failed to load the model or tokenizer. Error: {e}")
    exit()

# --- PREPARE A REALISTIC FinQA PROMPT ---
# FinQA has a specific structure: pre_text, table, post_text, question
prompt = """
[PRE_TEXT]
the following table shows the total assets and total liabilities of the company ( in millions ).
[TABLE]
year | total assets | total liabilities
2018 | 100 | 50
2019 | 120 | 60
2020 | 140 | 70
[POST_TEXT]
the table shows the financial data for the last three years.
[QUESTION]
what is the total assets in 2019?
"""

inputs = tokenizer(prompt, return_tensors="pt").to(device)

# --- DIAGNOSTIC TEST ---
print("\n--- RUNNING DIAGNOSTIC GENERATION ---")
outputs = model.generate(**inputs, max_new_tokens=25, pad_token_id=tokenizer.eos_token_id)

# --- ANALYSIS OF RESULTS ---
print("\n--- DIAGNOSTIC RESULTS ---")

# 1. Decode the output, KEEPING special tokens
print("\n[Analysis 1] Decoded output (including special tokens):")
decoded_output = tokenizer.decode(outputs[0], skip_special_tokens=False)
print(f"'{decoded_output}'")

# 2. Check for immediate EOS token
print("\n[Analysis 2] Checking for immediate End-of-Sequence token:")
prompt_length = len(inputs.input_ids[0])
first_generated_token_id = outputs[0][prompt_length].item()
eos_token_id = tokenizer.eos_token_id
print(f"First generated token ID: {first_generated_token_id}")
print(f"Official EOS token ID: {eos_token_id}")

if first_generated_token_id == eos_token_id:
    print("\n[CONCLUSION] >>> FAILURE: The model immediately generated the EOS token. The training failed even with longer sequences.")
else:
    print("\n[CONCLUSION] >>> SUCCESS (Potentially): The model is generating content. Review the output in Analysis 1 for quality and correctness.")