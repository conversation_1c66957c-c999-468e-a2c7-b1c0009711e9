from transformers import AutoTokenizer, AutoModelForCausalLM
import datasets
import dotenv
import os
import warnings
import concurrent.futures
import json
import re
from sklearn.metrics import precision_score, recall_score, f1_score, accuracy_score
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Union
import random
import csv
import torch

warnings.filterwarnings("ignore")

def normalize_answer(answer_text):
    """
    Normalize answer text to extract just the numerical value
    """
    if not answer_text or answer_text in [None, "None", ""]:
        return None
    
    # Convert to string if it's not already
    answer_str = str(answer_text).strip()
    
    # Handle multiple values in one answer (take the first one)
    if " and " in answer_str:
        answer_str = answer_str.split(" and ")[0]
    
    # Remove common prefixes/suffixes
    answer_str = answer_str.replace("$", "").replace(",", "")
    
    # Extract percentage
    if "%" in answer_str:
        # Extract number before %
        match = re.search(r'([\d.-]+)%?', answer_str)
        if match:
            return match.group(1)
    
    # Extract millions notation
    if "million" in answer_str.lower():
        # Look for number before "million"
        match = re.search(r'([\d.-]+)\s*million', answer_str.lower())
        if match:
            return match.group(1)
    
    # Extract basic number (with potential decimal)
    match = re.search(r'-?[\d,]+\.?\d*', answer_str)
    if match:
        return match.group(0).replace(",", "")
    
    # If no number found, return the original (might be text answer)
    return answer_str

def is_answer_parsable(answer):
    """Check if an answer is parsable (can be normalized to a valid value)"""
    normalized = normalize_answer(answer)
    if normalized is None:
        return False
    
    # Try to convert to float to check if it's a valid number
    try:
        float(normalized)
        return True
    except (ValueError, TypeError):
        # If it's not a number, consider it parsable if it's a non-empty string
        return len(str(normalized).strip()) > 0

def are_answers_equivalent(predicted, ground_truth, tolerance=0.01):
    """
    Check if predicted answer matches ground truth within tolerance
    """
    # Normalize both answers
    pred_norm = normalize_answer(predicted)
    gt_norm = normalize_answer(ground_truth)
    
    if pred_norm is None or gt_norm is None:
        return False
    
    try:
        # Try to convert to float for numerical comparison
        pred_float = float(pred_norm)
        gt_float = float(gt_norm)
        
        # Check if they're close enough (within tolerance)
        if abs(gt_float) < 1e-10:  # Ground truth is essentially zero
            return abs(pred_float) < tolerance
        else:
            return abs(pred_float - gt_float) / abs(gt_float) < tolerance
    
    except (ValueError, TypeError):
        # If conversion fails, do string comparison
        return pred_norm.lower().strip() == gt_norm.lower().strip()

# Load environment variables
dotenv.load_dotenv('../../.env')

# Load FinQA dataset (same as teacher)
print("Loading FinQA dataset...")
ds = datasets.load_dataset("MothMalone/SLMS-KD-Benchmarks", "finqa")

# Use train and validation splits (same as teacher)
train_data = ds['train']
val_data = ds['validation']

print(f"Loaded {len(train_data)} training samples and {len(val_data)} validation samples.")

DATASET_CONTEXT = """
FinQA is a financial question answering dataset that requires numerical reasoning over financial documents.
Each question is based on financial reports containing tables and text. You need to analyze the provided
financial information and answer the question with precise numerical calculations.
"""

# Define the student model ID
STUDENT_MODEL_ID = "MothMalone/Llama3.2-3B-to-1B-FinQA-Distilled"

# Load the student model and tokenizer
print("Loading student model...")
tokenizer = AutoTokenizer.from_pretrained(STUDENT_MODEL_ID)
model = AutoModelForCausalLM.from_pretrained(
    STUDENT_MODEL_ID,
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto" if torch.cuda.is_available() else None
)

# Set pad token if not exists
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

print(f"Model loaded. Device: {model.device if hasattr(model, 'device') else 'CPU'}")

def format_table(table_data):
    if not table_data or len(table_data) == 0:
        return "No table data provided."
    
    formatted_table = []
    for row in table_data:
        if isinstance(row, list):
            formatted_table.append(" | ".join(str(cell) for cell in row))
        else:
            formatted_table.append(str(row))
    
    return "\n".join(formatted_table)

def format_text_sequences(text_seq):
    if not text_seq:
        return ""
    if isinstance(text_seq, list):
        return "\n".join(text_seq)
    return str(text_seq)

def extract_answer_from_response(response_text):
    """
    Extract structured answer from model response using multiple strategies
    """
    response_text = str(response_text).strip()
    
    # Strategy 1: Look for JSON-like structure
    json_patterns = [
        r'"answer":\s*"([^"]*)"',
        r"'answer':\s*'([^']*)'",
        r'answer"?\s*:\s*"?([^",}]+)"?',
        r'"reasoning".*?"answer":\s*"([^"]*)"',
    ]
    
    for pattern in json_patterns:
        match = re.search(pattern, response_text, re.IGNORECASE | re.DOTALL)
        if match:
            answer = match.group(1).strip()
            if answer and answer.lower() not in ['null', 'none', '']:
                return answer
    
    # Strategy 2: Look for explicit answer markers
    answer_patterns = [
        r'final answer[:\s]+([^\n\r\.]+)',
        r'answer[:\s]+([^\n\r\.]+)',
        r'result[:\s]+([^\n\r\.]+)',
        r'solution[:\s]+([^\n\r\.]+)',
    ]
    
    for pattern in answer_patterns:
        match = re.search(pattern, response_text, re.IGNORECASE)
        if match:
            answer = match.group(1).strip()
            if answer and answer.lower() not in ['null', 'none', '']:
                return answer
    
    # Strategy 3: Look for numbers at the end of response
    end_patterns = [
        r'(?:^|\n)\s*([+-]?[\d,]+\.?\d*)\s*$',
        r'(?:^|\n)\s*([+-]?[\d,]+\.?\d*)\s*(?:\.|$)',
    ]
    
    for pattern in end_patterns:
        match = re.search(pattern, response_text, re.MULTILINE)
        if match:
            return match.group(1).strip()
    
    # Strategy 4: Extract any number from the response
    number_match = re.search(r'([+-]?[\d,]+\.?\d*)', response_text)
    if number_match:
        return number_match.group(1).strip()
    
    return None

def extract_reasoning_from_response(response_text):
    """
    Extract reasoning from model response
    """
    response_text = str(response_text).strip()
    
    # Strategy 1: Look for JSON reasoning field
    reasoning_patterns = [
        r'"reasoning":\s*"([^"]*)"',
        r"'reasoning':\s*'([^']*)'",
        r'reasoning"?\s*:\s*"?([^",}]+)"?',
    ]
    
    for pattern in reasoning_patterns:
        match = re.search(pattern, response_text, re.IGNORECASE | re.DOTALL)
        if match:
            reasoning = match.group(1).strip()
            if reasoning and reasoning.lower() not in ['null', 'none', '']:
                return reasoning
    
    # Strategy 2: Use the entire response as reasoning if no specific reasoning found
    return response_text[:500]  # Truncate to avoid too long reasoning

def predict_answer_student(finqa_data, debug=False):
    """Generate prediction using the student model with the same prompt as teacher."""
    if debug:
        print("\n" + "=" * 60)
        print(f"DEBUG INFO FOR SAMPLE ID: {finqa_data.get('id', 'unknown')}")
        print(f"Question: {finqa_data['question']}")
        print(f"Ground Truth: {finqa_data.get('final_result', 'N/A')}")
        print(f"Gold Instructions (gold_inds): {finqa_data.get('gold_inds', 'N/A')}")
        print(f"Program Reasoning (program_re): {finqa_data.get('program_re', 'N/A')}")
        print("=" * 60 + "\n")

    # Format the input data (same as teacher)
    pre_text = format_text_sequences(finqa_data.get('pre_text', []))
    post_text = format_text_sequences(finqa_data.get('post_text', []))
    table = format_table(finqa_data.get('table', []))
    question = finqa_data['question']

    # Use the exact same prompt structure as teacher model
    prompt = f"""You are a financial analyst. You must analyze the provided financial data and answer the question with precise calculations.

FINANCIAL DATA:
{pre_text}

{table}

{post_text}

QUESTION: {question}

INSTRUCTIONS:
1. Read the financial data carefully
2. Identify the specific numbers mentioned in the question
3. Find those exact numbers in the provided data
4. Perform the calculation step by step
5. CRITICAL: Your answer field must contain ONLY the final numerical value, no units, no extra text

ANSWER FORMAT EXAMPLES:
- For percentages: "15.2" (not "15.2%" or "15.2 percent")
- For millions: "41932" (not "$41932 million" or "41932 million")
- For ratios: "0.532" (not "53.2%" unless specifically asked for percentage)
- For negative values: "-32.1" (not "-$32.1 million")

CRITICAL: Extract only the core numerical value for the answer field. All explanations go in reasoning.

OUTPUT FORMAT (strict JSON):
```json
{{
"reasoning": "<show your step-by-step calculation here>",
"answer": "<ONLY the final numerical value, no units or formatting>"
}}
``` """

    try:
        # Tokenize input
        inputs = tokenizer.encode(prompt, return_tensors="pt", truncation=True, max_length=2048)
        
        # Move to same device as model
        if hasattr(model, 'device'):
            inputs = inputs.to(model.device)
        
        # Generate response with same parameters as teacher (temperature=0.1, top_p=0.9)
        with torch.no_grad():
            outputs = model.generate(
                inputs,
                max_new_tokens=512,    # Same as teacher
                temperature=0.1,      # Same as teacher
                top_p=0.9,           # Same as teacher
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
            )
        
        # Decode response
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract only the generated part (after the prompt)
        prompt_length = len(tokenizer.decode(inputs[0], skip_special_tokens=True))
        generated_text = response[prompt_length:].strip()
        
        # Extract structured answer and reasoning
        predicted_answer = extract_answer_from_response(generated_text)
        reasoning = extract_reasoning_from_response(generated_text)
        
        # Clean formatting for gold_inds and program_re (same as teacher)
        gold_inds = finqa_data.get('gold_inds', [])
        if isinstance(gold_inds, str):
            try:
                gold_inds = json.loads(gold_inds)
            except Exception:
                gold_inds = []
        if not isinstance(gold_inds, list):
            gold_inds = [gold_inds]
        gold_inds = [int(i) for i in gold_inds if isinstance(i, (int, float, str)) and str(i).isdigit()]

        program_re = finqa_data.get('program_re', "")
        if not isinstance(program_re, str):
            program_re = str(program_re)

        return {
            "answer": predicted_answer,
            "reasoning": reasoning,
            "gold_inds": gold_inds,
            "program_re": program_re,
            "ground_truth": finqa_data.get('final_result', None)
        }

    except Exception as e:
        print(f"\nError generating answer: {str(e)}\n")
        # Try fallback with simpler prompt (same as teacher)
        try:
            print("Trying fallback without structured output...\n")
            fallback_prompt = f"""Based on this financial data, answer the question with just a number:

{pre_text}
{table}
{post_text}

Question: {question}

Answer with only the numerical value:"""

            # Tokenize fallback prompt
            inputs = tokenizer.encode(fallback_prompt, return_tensors="pt", truncation=True, max_length=2048)
            
            if hasattr(model, 'device'):
                inputs = inputs.to(model.device)
            
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_new_tokens=50,   # Same as teacher fallback
                    temperature=0.1,
                    top_p=0.9,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            prompt_length = len(tokenizer.decode(inputs[0], skip_special_tokens=True))
            answer = response[prompt_length:].strip()
            
            # Clean formatting for gold_inds and program_re
            gold_inds = finqa_data.get('gold_inds', [])
            if isinstance(gold_inds, str):
                try:
                    gold_inds = json.loads(gold_inds)
                except Exception:
                    gold_inds = []
            if not isinstance(gold_inds, list):
                gold_inds = [gold_inds]
            gold_inds = [int(i) for i in gold_inds if isinstance(i, (int, float, str)) and str(i).isdigit()]

            program_re = finqa_data.get('program_re', "")
            if not isinstance(program_re, str):
                program_re = str(program_re)

            return {
                "answer": answer,
                "reasoning": "Fallback prediction",
                "gold_inds": gold_inds,
                "program_re": program_re,
                "ground_truth": finqa_data.get('final_result', None)
            }

        except Exception as e2:
            print(f"Fallback also failed: {str(e2)}\n")
            return {
                "answer": None,
                "reasoning": None,
                "gold_inds": [],
                "program_re": "",
                "ground_truth": finqa_data.get('final_result', None)
            }

def process_finqa_sample_student(sample_data, debug=False):
    """Process a single FinQA sample with student model and return results."""
    pred = predict_answer_student(sample_data, debug=debug)

    if pred["answer"] is None:
        return {
            'id': sample_data.get('id', 'unknown'),
            'question': sample_data['question'],
            'ground_truth': pred["ground_truth"],
            'predicted_answer': None,
            'reasoning': pred["reasoning"],
            'gold_inds': pred["gold_inds"],
            'program_re': pred["program_re"],
            'success': False,
            'is_parsable': False
        }

    # Check if answer is parsable (same as teacher)
    is_parsable = is_answer_parsable(pred["answer"])

    return {
        'id': sample_data.get('id', 'unknown'),
        'question': sample_data['question'],
        'ground_truth': pred["ground_truth"],
        'predicted_answer': pred["answer"],
        'reasoning': pred["reasoning"],
        'gold_inds': pred["gold_inds"],
        'program_re': pred["program_re"],
        'success': True,
        'is_parsable': is_parsable
    }

# Keep all the same utility functions from teacher
def calculate_current_metrics(results):
    """Calculate current accuracy and invalid answer count"""
    total_processed = len(results)
    valid_answers = [r for r in results if r['success'] and r['is_parsable']]
    invalid_answers = [r for r in results if not r['success'] or not r['is_parsable']]
    
    if len(valid_answers) == 0:
        return 0.0, len(invalid_answers), total_processed
    
    correct = 0
    for result in valid_answers:
        if are_answers_equivalent(result['predicted_answer'], result['ground_truth']):
            correct += 1
    
    accuracy = correct / len(valid_answers)
    return accuracy, len(invalid_answers), total_processed

def write_to_csv(results, filename="finqa_student_results.csv"):
    """Write results to CSV file"""
    fieldnames = ['id', 'question', 'ground_truth', 'predicted_answer', 'reasoning', 'gold_inds', 'program_re', 'success', 'is_parsable']
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for result in results:
            # Convert lists to string for CSV
            csv_result = result.copy()
            csv_result['gold_inds'] = json.dumps(result['gold_inds']) if result['gold_inds'] else "[]"
            
            # Clean up text fields to avoid CSV issues
            for field in ['question', 'reasoning']:
                if csv_result[field]:
                    csv_result[field] = str(csv_result[field]).replace('"', '""').replace('\n', ' ')
            
            writer.writerow(csv_result)

def write_metrics_to_csv(metrics, filename="finqa_student_metrics_log.csv"):
    fieldnames = [
        "accuracy", "successful", "invalid_incorrect", "correct"
    ]
    file_exists = os.path.isfile(filename)
    with open(filename, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        if not file_exists:
            writer.writeheader()
        # Only keep the relevant fields from metrics
        filtered_metrics = {k: metrics[k] for k in fieldnames if k in metrics}
        writer.writerow(filtered_metrics)

def write_simple_metrics_to_csv(steps, successful, unresolved, correct, invalid_incorrect, filename="finqa_student_simple_metrics.csv"):
    fieldnames = [
        "Steps", "Successful samples", "Unresolved samples", "Correct samples", "Invalid/Incorrect cases"
    ]
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(fieldnames)
        writer.writerow([
            steps,
            successful,
            unresolved,
            correct,
            invalid_incorrect
        ])

def create_combined_dataset(train_data, val_data, total_samples=1000):
    """Create a combined dataset with samples from both train and validation (same as teacher)"""
    # Calculate how many samples to take from each split
    train_samples = min(500, len(train_data))  # Take up to 500 from train
    val_samples = min(total_samples - train_samples, len(val_data))  # Fill remaining from validation
    
    # If validation doesn't have enough, take more from train
    if val_samples < (total_samples - train_samples):
        remaining_needed = total_samples - train_samples - val_samples
        train_samples = min(train_samples + remaining_needed, len(train_data))
    
    print(f"Taking {train_samples} samples from train and {val_samples} samples from validation")
    
    # Randomly select samples (same seed as teacher for fair comparison)
    train_indices = random.sample(range(len(train_data)), train_samples)
    val_indices = random.sample(range(len(val_data)), val_samples)
    
    # Combine samples with source information
    combined_samples = []
    for idx in train_indices:
        sample = train_data[idx]
        sample_dict = dict(sample)
        sample_dict['source'] = 'train'
        sample_dict['original_index'] = idx
        combined_samples.append(sample_dict)
    
    for idx in val_indices:
        sample = val_data[idx]
        sample_dict = dict(sample)
        sample_dict['source'] = 'validation'
        sample_dict['original_index'] = idx
        combined_samples.append(sample_dict)
    
    # Shuffle the combined samples
    random.shuffle(combined_samples)
    
    return combined_samples

def run_1000_sample_evaluation_student(train_data, val_data, max_workers=4):
    """Run evaluation on 1000 samples from both train and validation splits using student model."""
    print(f"\n=== Running STUDENT MODEL evaluation on 1000 samples from both train and validation ===\n")
    
    # Set same random seed as teacher for fair comparison
    random.seed(42)
    
    # Create combined dataset (same as teacher)
    combined_data = create_combined_dataset(train_data, val_data, 1000)
    print(f"Created combined dataset with {len(combined_data)} samples")
    
    results = []
    csv_filename = "finqa_student_1000_samples_results.csv"
    
    print(f"Starting student model evaluation...")
    print("=" * 80)
    
    # Process samples sequentially for student model to avoid GPU memory issues
    # But still maintain the same logging structure
    for i, sample_data in enumerate(combined_data):
        try:
            result = process_finqa_sample_student(sample_data, debug=(i < 2))
            results.append(result)
            
            # Live logging every 5 samples (same as teacher)
            if (i + 1) % 5 == 0 or (i + 1) == len(combined_data):
                current_accuracy, invalid_count, total_processed = calculate_current_metrics(results)
                successful = sum(1 for r in results if r['success'] and r['is_parsable'])
                unresolved = sum(1 for r in results if not r['success'] or not r['is_parsable'])
                correct = sum(1 for r in results if r['success'] and r['is_parsable'] and are_answers_equivalent(r['predicted_answer'], r['ground_truth']))
                invalid_incorrect = successful - correct + unresolved
                
                # Write current results to CSV
                write_to_csv(results, csv_filename)
                
                # Log metrics to metrics CSV
                metrics_row = {
                    "step": i + 1,
                    "accuracy": f"{current_accuracy*100:.2f}",
                    "successful": successful,
                    "invalid_incorrect": invalid_incorrect,
                    "correct": correct
                }
                write_metrics_to_csv(metrics_row)
                
                print(f"\n--- STUDENT MODEL Progress Update: {i + 1}/{len(combined_data)} samples processed ---")
                print(f"Current Accuracy: {current_accuracy:.4f} ({current_accuracy*100:.2f}%)")
                print(f"Successful samples: {successful} ({successful/total_processed*100:.2f}%)")
                print(f"Unresolved samples: {unresolved} ({unresolved/total_processed*100:.2f}%)")
                
                # Show last 5 sample IDs
                recent_samples = results[-5:]
                sample_ids = [r['id'] for r in recent_samples]
                print(f"Recent Sample IDs: {sample_ids}")
                
                # Show details of the most recent sample
                if results:
                    last_result = results[-1]
                    print(f"Last Sample Details:")
                    print(f"  ID: {last_result['id']}")
                    print(f"  Question: {last_result['question'][:100]}...")
                    print(f"  Ground Truth: {last_result['ground_truth']}")
                    print(f"  Predicted: {last_result['predicted_answer']}")
                    print(f"  Parsable: {last_result['is_parsable']}")
                    if last_result['is_parsable'] and last_result['ground_truth']:
                        is_correct = are_answers_equivalent(last_result['predicted_answer'], last_result['ground_truth'])
                        print(f"  Correct: {is_correct}")
                print("-" * 80)
                
        except Exception as e:
            print(f"\nError processing sample {i}: {str(e)}\n")
            # Add a failed result
            results.append({
                'id': f'failed_{i}',
                'question': 'Failed to process',
                'ground_truth': None,
                'predicted_answer': None,
                'reasoning': None,
                'gold_inds': [],
                'program_re': '',
                'success': False,
                'is_parsable': False
            })
    
    # Final CSV write
    write_to_csv(results, csv_filename)
    
    # Save results to JSON file as well
    results_file = "finqa_student_1000_samples_results.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Calculate final metrics
    final_accuracy, final_invalid_count, total_processed = calculate_current_metrics(results)
    valid_count = total_processed - final_invalid_count
    
    print(f"\n" + "=" * 80)
    print(f"STUDENT MODEL FINAL EVALUATION RESULTS")
    print(f"=" * 80)
    print(f"Model: {STUDENT_MODEL_ID}")
    print(f"Total samples processed: {total_processed}")
    print(f"Valid answers: {valid_count}")
    print(f"Invalid/unparsable answers: {final_invalid_count}")
    print(f"Final accuracy (on valid answers): {final_accuracy:.4f} ({final_accuracy*100:.2f}%)")
    print(f"Success rate: {(total_processed - final_invalid_count)/total_processed*100:.2f}%")
    print(f"Results saved to CSV: {csv_filename}")
    print(f"Results saved to JSON: {results_file}")
    print(f"=" * 80)
    
    # Show some examples of invalid answers
    invalid_results = [r for r in results if not r['success'] or not r['is_parsable']]
    if invalid_results:
        print(f"\nExamples of invalid answers:")
        for i, result in enumerate(invalid_results[:5]):
            print(f"  {i+1}. ID: {result['id']}, Answer: {result['predicted_answer']}")
    
    # Save final summary for comparison
    final_summary = {
        "model": "student",
        "model_id": STUDENT_MODEL_ID,
        "total_samples": total_processed,
        "valid_answers": valid_count,
        "invalid_answers": final_invalid_count,
        "accuracy": round(final_accuracy * 100, 2),
        "success_rate": round((total_processed - final_invalid_count)/total_processed*100, 2)
    }
    
    with open("finqa_student_final_summary.json", "w") as f:
        json.dump(final_summary, f, indent=2)
    
    return results, final_accuracy

if __name__ == "__main__":
    print("Running STUDENT MODEL evaluation on 1000 samples from both train and validation splits...")
    results, accuracy = run_1000_sample_evaluation_student(train_data, val_data, max_workers=4)
    print(f"\nSTUDENT MODEL evaluation completed!")
    print(f"Final accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")